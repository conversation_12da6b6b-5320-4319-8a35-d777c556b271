<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YJS Library Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 10px 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🧪 YJS Library Test</h1>
    
    <div id="socketStatus" class="status info">Testing Socket.IO...</div>
    <div id="yjsStatus" class="status info">Testing YJS...</div>
    
    <button id="testConnection" class="btn-primary">Test Server Connection</button>
    
    <h3>Test Log</h3>
    <div id="log" class="log"></div>

    <!-- Test library loading -->
    <script src="/public/js/socket.io.min.js"></script>
    <script>
        // Test Socket.IO first
        if (typeof io !== 'undefined') {
            document.getElementById('socketStatus').textContent = '✅ Socket.IO loaded successfully';
            document.getElementById('socketStatus').className = 'status success';
            log('✅ Socket.IO library loaded');
        } else {
            document.getElementById('socketStatus').textContent = '❌ Socket.IO failed to load';
            document.getElementById('socketStatus').className = 'status error';
            log('❌ Socket.IO library failed to load');
        }
    </script>
    
    <!-- Try to load YJS with different approaches -->
    <script type="module">
        // Try loading YJS as ES module
        try {
            const response = await fetch('/public/js/yjs.js');
            const yjsCode = await response.text();
            
            // Create a script element and execute the YJS code
            const script = document.createElement('script');
            script.textContent = yjsCode;
            document.head.appendChild(script);
            
            // Check if Y is available
            setTimeout(() => {
                if (typeof Y !== 'undefined') {
                    document.getElementById('yjsStatus').textContent = '✅ YJS loaded successfully';
                    document.getElementById('yjsStatus').className = 'status success';
                    log('✅ YJS library loaded');
                    
                    // Test YJS functionality
                    try {
                        const doc = new Y.Doc();
                        const text = doc.getText('test');
                        text.insert(0, 'Hello YJS!');
                        log('✅ YJS functionality test passed');
                    } catch (error) {
                        log('❌ YJS functionality test failed: ' + error.message);
                    }
                } else {
                    document.getElementById('yjsStatus').textContent = '❌ YJS failed to load';
                    document.getElementById('yjsStatus').className = 'status error';
                    log('❌ YJS library failed to load - Y is not defined');
                }
            }, 100);
            
        } catch (error) {
            document.getElementById('yjsStatus').textContent = '❌ YJS failed to load';
            document.getElementById('yjsStatus').className = 'status error';
            log('❌ Failed to fetch YJS: ' + error.message);
        }
    </script>
    
    <script>
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logEl.appendChild(entry);
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        document.getElementById('testConnection').addEventListener('click', () => {
            if (typeof io === 'undefined') {
                log('❌ Cannot test connection - Socket.IO not loaded');
                return;
            }
            
            log('🔌 Testing server connection...');
            
            const socket = io('http://localhost:3000');
            
            socket.on('connect', () => {
                log('✅ Server connection successful');
                socket.disconnect();
            });
            
            socket.on('connect_error', (error) => {
                log('❌ Server connection failed: ' + error.message);
            });
            
            // Timeout after 5 seconds
            setTimeout(() => {
                if (!socket.connected) {
                    log('❌ Connection timeout');
                    socket.disconnect();
                }
            }, 5000);
        });
        
        // Initial log
        log('🚀 Test page loaded');
    </script>
</body>
</html>
