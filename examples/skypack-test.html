<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skypack YJS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🧪 Skypack YJS Test</h1>
    
    <div id="status" class="status info">Loading YJS from Skypack...</div>
    
    <h3>Test Log</h3>
    <div id="log" class="log"></div>

    <!-- Load Socket.IO -->
    <script src="/public/js/socket.io.min.js"></script>
    
    <!-- Load YJS from Skypack -->
    <script type="module">
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            if (type === 'error') entry.style.color = 'red';
            if (type === 'success') entry.style.color = 'green';
            logEl.appendChild(entry);
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        try {
            log('📦 Loading YJS from Skypack...');
            
            const Y = await import('https://cdn.skypack.dev/yjs@13.6.10');
            window.Y = Y;
            
            log('✅ YJS loaded successfully', 'success');
            document.getElementById('status').textContent = '✅ YJS loaded from Skypack';
            document.getElementById('status').className = 'status success';
            
            // Test YJS functionality
            const doc = new Y.Doc();
            const text = doc.getText('test');
            text.insert(0, 'Hello from Skypack YJS!');
            
            log('✅ YJS functionality test passed: ' + text.toString(), 'success');
            
            // Test Socket.IO
            if (typeof io !== 'undefined') {
                log('✅ Socket.IO is available', 'success');
                
                // Test server connection
                log('🔌 Testing server connection...');
                const socket = io('http://localhost:3000');
                
                socket.on('connect', () => {
                    log('✅ Server connection successful', 'success');
                    
                    // Test document join
                    socket.emit('join-document', {
                        documentId: 'skypack-test',
                        userId: 'skypack-user'
                    });
                });
                
                socket.on('joined-document', (data) => {
                    log('✅ Successfully joined document: ' + data.documentId, 'success');
                    socket.disconnect();
                    log('🎉 All tests passed!', 'success');
                });
                
                socket.on('connect_error', (error) => {
                    log('❌ Server connection failed: ' + error.message, 'error');
                });
                
            } else {
                log('❌ Socket.IO not available', 'error');
            }
            
        } catch (error) {
            log('❌ Failed to load YJS: ' + error.message, 'error');
            document.getElementById('status').textContent = '❌ Failed to load YJS';
            document.getElementById('status').className = 'status error';
        }
    </script>
</body>
</html>
