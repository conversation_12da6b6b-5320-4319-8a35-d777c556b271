<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple YJS Client Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .editor {
            width: 100%;
            height: 200px;
            border: 1px solid #ccc;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
        }
        .log {
            height: 200px;
            overflow-y: scroll;
            border: 1px solid #ccc;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-primary:disabled { background-color: #ccc; }
    </style>
</head>
<body>
    <h1>Simple YJS Realtime Collaboration Test</h1>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <div>
        <input type="text" id="documentId" value="test-document" placeholder="Document ID" />
        <input type="text" id="userId" value="" placeholder="User ID" />
        <button id="connectBtn" class="btn-primary">Connect</button>
        <button id="disconnectBtn" class="btn-secondary" disabled>Disconnect</button>
    </div>
    
    <div>
        <h3>Shared Text (Type here to test real-time sync)</h3>
        <textarea id="editor" class="editor" placeholder="Start typing to test collaboration..." disabled></textarea>
    </div>
    
    <div>
        <h3>Connection Log</h3>
        <div id="log" class="log"></div>
    </div>

    <!-- Use Socket.IO from CDN -->
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    
    <script>
        // Simple client without YJS for basic connection testing
        class SimpleClient {
            constructor() {
                this.socket = null;
                this.connected = false;
                this.documentId = null;
                this.userId = null;
                
                this.initializeElements();
                this.setupEventListeners();
                this.generateUserId();
            }
            
            initializeElements() {
                this.statusEl = document.getElementById('status');
                this.documentIdEl = document.getElementById('documentId');
                this.userIdEl = document.getElementById('userId');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.editorEl = document.getElementById('editor');
                this.logEl = document.getElementById('log');
            }
            
            generateUserId() {
                this.userIdEl.value = 'user-' + Math.random().toString(36).substr(2, 9);
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                
                this.editorEl.addEventListener('input', (e) => {
                    if (this.connected && this.socket) {
                        // Send simple text update (not YJS)
                        this.socket.emit('text-update', {
                            documentId: this.documentId,
                            content: e.target.value,
                            userId: this.userId
                        });
                    }
                });
            }
            
            connect() {
                const documentId = this.documentIdEl.value.trim();
                const userId = this.userIdEl.value.trim();
                
                if (!documentId || !userId) {
                    alert('Please enter both Document ID and User ID');
                    return;
                }
                
                this.documentId = documentId;
                this.userId = userId;
                
                this.log('Connecting to server...');
                
                this.socket = io('http://localhost:3000', {
                    transports: ['websocket', 'polling'],
                    timeout: 10000
                });
                
                this.socket.on('connect', () => {
                    this.log('✅ Connected to server');
                    this.updateConnectionStatus(true);
                    
                    // Try to join document
                    this.socket.emit('join-document', {
                        documentId: this.documentId,
                        userId: this.userId
                    });
                });
                
                this.socket.on('disconnect', (reason) => {
                    this.log(`❌ Disconnected: ${reason}`);
                    this.updateConnectionStatus(false);
                });
                
                this.socket.on('connect_error', (error) => {
                    this.log(`❌ Connection error: ${error.message}`, 'error');
                    this.updateConnectionStatus(false);
                });
                
                this.socket.on('joined-document', (data) => {
                    this.log(`🏠 Joined document: ${data.documentId}`);
                });
                
                this.socket.on('user-joined', (data) => {
                    this.log(`👋 User joined: ${data.userId}`);
                });
                
                this.socket.on('user-left', (data) => {
                    this.log(`👋 User left: ${data.userId}`);
                });
                
                this.socket.on('error', (error) => {
                    this.log(`❌ Server error: ${error.message}`, 'error');
                });
                
                // Listen for simple text updates from other users
                this.socket.on('text-update', (data) => {
                    if (data.userId !== this.userId) {
                        this.log(`📝 Text update from ${data.userId}`);
                        // Don't update editor to avoid conflicts in this simple version
                    }
                });
            }
            
            disconnect() {
                if (this.socket) {
                    this.socket.disconnect();
                }
                this.updateConnectionStatus(false);
            }
            
            updateConnectionStatus(connected) {
                this.connected = connected;
                this.statusEl.textContent = connected ? 'Connected' : 'Disconnected';
                this.statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
                this.editorEl.disabled = !connected;
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
                
                if (type === 'error') {
                    logEntry.style.color = 'red';
                } else if (type === 'warning') {
                    logEntry.style.color = 'orange';
                }
                
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
        }
        
        // Initialize the simple client
        const client = new SimpleClient();
        
        // Test server connection on page load
        window.addEventListener('load', () => {
            client.log('🚀 Simple client loaded');
            client.log('Click Connect to test server connection');
            
            // Test if server is reachable
            fetch('http://localhost:3000/health')
                .then(response => response.json())
                .then(data => {
                    client.log(`✅ Server health check: ${data.status}`);
                })
                .catch(error => {
                    client.log(`❌ Server health check failed: ${error.message}`, 'error');
                    client.log('Make sure the server is running on http://localhost:3000', 'warning');
                });
        });
    </script>
</body>
</html>
