<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YJS Realtime Collaboration Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .editor {
            width: 100%;
            height: 200px;
            border: 1px solid #ccc;
            padding: 10px;
            font-family: monospace;
        }
        .users {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .user {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background-color: #007bff;
            color: white;
            border-radius: 12px;
            font-size: 12px;
        }
        .stats {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>YJS Realtime Collaboration Example</h1>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <div>
        <label for="documentId">Document ID:</label>
        <input type="text" id="documentId" value="example-document" />
        <label for="userId">User ID:</label>
        <input type="text" id="userId" value="" />
        <button id="joinBtn">Join Document</button>
        <button id="leaveBtn" disabled>Leave Document</button>
    </div>
    
    <div class="users">
        <strong>Connected Users:</strong>
        <div id="userList"></div>
    </div>
    
    <div>
        <h3>Shared Text Editor</h3>
        <textarea id="editor" class="editor" placeholder="Start typing to collaborate in real-time..."></textarea>
    </div>
    
    <div class="stats">
        <strong>Statistics:</strong>
        <div id="stats"></div>
    </div>
    
    <div>
        <h3>Event Log</h3>
        <div id="log" style="height: 200px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <script src="https://unpkg.com/yjs@13.6.10"></script>
    <script>
        class YjsCollaborationClient {
            constructor() {
                this.socket = null;
                this.doc = new Y.Doc();
                this.text = this.doc.getText('content');
                this.connected = false;
                this.documentId = null;
                this.userId = null;
                this.users = new Set();
                
                this.initializeElements();
                this.setupEventListeners();
                this.generateUserId();
            }
            
            initializeElements() {
                this.statusEl = document.getElementById('status');
                this.documentIdEl = document.getElementById('documentId');
                this.userIdEl = document.getElementById('userId');
                this.joinBtn = document.getElementById('joinBtn');
                this.leaveBtn = document.getElementById('leaveBtn');
                this.editorEl = document.getElementById('editor');
                this.userListEl = document.getElementById('userList');
                this.statsEl = document.getElementById('stats');
                this.logEl = document.getElementById('log');
            }
            
            generateUserId() {
                this.userIdEl.value = 'user-' + Math.random().toString(36).substr(2, 9);
            }
            
            setupEventListeners() {
                this.joinBtn.addEventListener('click', () => this.joinDocument());
                this.leaveBtn.addEventListener('click', () => this.leaveDocument());
                
                // YJS text change listener
                this.text.observe((event) => {
                    if (!this.updatingFromRemote) {
                        this.editorEl.value = this.text.toString();
                    }
                });
                
                // Editor change listener
                this.editorEl.addEventListener('input', (e) => {
                    if (!this.updatingFromRemote) {
                        const content = e.target.value;
                        this.text.delete(0, this.text.length);
                        this.text.insert(0, content);
                    }
                });
            }
            
            joinDocument() {
                const documentId = this.documentIdEl.value.trim();
                const userId = this.userIdEl.value.trim();
                
                if (!documentId || !userId) {
                    alert('Please enter both Document ID and User ID');
                    return;
                }
                
                this.documentId = documentId;
                this.userId = userId;
                
                this.connect();
            }
            
            leaveDocument() {
                if (this.socket) {
                    this.socket.disconnect();
                }
                this.updateConnectionStatus(false);
            }
            
            connect() {
                this.log('Connecting to server...');

                // Check if YJS is loaded
                if (typeof Y === 'undefined') {
                    this.log('Error: YJS library not loaded!', 'error');
                    alert('YJS library failed to load. Please check your internet connection and refresh the page.');
                    return;
                }

                this.socket = io('http://localhost:3000', {
                    transports: ['websocket', 'polling'],
                    timeout: 10000
                });
                
                this.socket.on('connect', () => {
                    this.log('Connected to server');
                    this.updateConnectionStatus(true);
                    
                    this.socket.emit('join-document', {
                        documentId: this.documentId,
                        userId: this.userId
                    });
                });
                
                this.socket.on('disconnect', (reason) => {
                    this.log(`Disconnected from server: ${reason}`);
                    this.updateConnectionStatus(false);
                });

                this.socket.on('connect_error', (error) => {
                    this.log(`Connection error: ${error.message}`, 'error');
                    this.updateConnectionStatus(false);
                });

                this.socket.on('reconnect', (attemptNumber) => {
                    this.log(`Reconnected after ${attemptNumber} attempts`);
                });

                this.socket.on('reconnect_error', (error) => {
                    this.log(`Reconnection error: ${error.message}`, 'error');
                });
                
                this.socket.on('joined-document', (data) => {
                    this.log(`Joined document: ${data.documentId} (${data.connectedUsers} users)`);
                });
                
                this.socket.on('yjs-update', (data) => {
                    if (data.origin !== this.socket.id) {
                        this.updatingFromRemote = true;
                        const update = new Uint8Array(data.update);
                        Y.applyUpdate(this.doc, update, this.socket.id);
                        this.editorEl.value = this.text.toString();
                        this.updatingFromRemote = false;
                        this.log('Received update from remote');
                    }
                });
                
                this.socket.on('sync-response', (data) => {
                    this.updatingFromRemote = true;
                    const state = new Uint8Array(data.state);
                    Y.applyUpdate(this.doc, state, this.socket.id);
                    this.editorEl.value = this.text.toString();
                    this.updatingFromRemote = false;
                    this.log(`Synced document (${data.type})`);
                });
                
                this.socket.on('user-joined', (data) => {
                    this.users.add(data.userId);
                    this.updateUserList();
                    this.log(`User joined: ${data.userId}`);
                });
                
                this.socket.on('user-left', (data) => {
                    this.users.delete(data.userId);
                    this.updateUserList();
                    this.log(`User left: ${data.userId}`);
                });
                
                this.socket.on('error', (error) => {
                    this.log(`Error: ${error.message}`, 'error');
                });
                
                this.socket.on('server-shutdown', (data) => {
                    this.log(`Server shutdown: ${data.message}`, 'warning');
                });
                
                // YJS document update listener
                this.doc.on('update', (update, origin) => {
                    if (origin !== this.socket.id && this.connected) {
                        this.socket.emit('yjs-update', {
                            documentId: this.documentId,
                            update: Array.from(update)
                        });
                        this.log('Sent update to server');
                    }
                });
                
                // Fetch stats periodically
                setInterval(() => {
                    if (this.connected) {
                        this.fetchStats();
                    }
                }, 5000);
            }
            
            updateConnectionStatus(connected) {
                this.connected = connected;
                this.statusEl.textContent = connected ? 'Connected' : 'Disconnected';
                this.statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.joinBtn.disabled = connected;
                this.leaveBtn.disabled = !connected;
                this.editorEl.disabled = !connected;
                
                if (!connected) {
                    this.users.clear();
                    this.updateUserList();
                }
            }
            
            updateUserList() {
                this.userListEl.innerHTML = '';
                this.users.forEach(userId => {
                    const userEl = document.createElement('span');
                    userEl.className = 'user';
                    userEl.textContent = userId;
                    this.userListEl.appendChild(userEl);
                });
            }
            
            fetchStats() {
                fetch('http://localhost:3000/api/stats')
                    .then(response => response.json())
                    .then(stats => {
                        this.statsEl.innerHTML = `
                            <div>Total Connections: ${stats.connections.totalConnections}</div>
                            <div>Documents: ${stats.documents.totalDocuments}</div>
                            <div>Last Updated: ${new Date(stats.timestamp).toLocaleTimeString()}</div>
                        `;
                    })
                    .catch(error => {
                        this.log(`Failed to fetch stats: ${error.message}`, 'error');
                    });
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
                
                if (type === 'error') {
                    logEntry.style.color = 'red';
                } else if (type === 'warning') {
                    logEntry.style.color = 'orange';
                }
                
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
        }
        
        // Initialize the client
        const client = new YjsCollaborationClient();
    </script>
</body>
</html>
