<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tiptap + YJS Collaboration Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .editor {
            border: 1px solid #ccc;
            border-radius: 4px;
            min-height: 200px;
            padding: 10px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .controls {
            margin: 20px 0;
        }
        .controls input, .controls button {
            margin: 5px;
            padding: 8px 12px;
        }
    </style>
</head>
<body>
    <h1>Tiptap + YJS Real-time Collaboration</h1>
    
    <div class="controls">
        <label>
            Document ID: 
            <input type="text" id="documentId" value="demo-document" placeholder="Enter document ID">
        </label>
        <label>
            User ID: 
            <input type="text" id="userId" value="" placeholder="Enter your name">
        </label>
        <button onclick="connectToDocument()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
    </div>

    <div id="status" class="status disconnected">Disconnected</div>
    
    <div id="editor" class="editor"></div>

    <div id="info">
        <h3>Instructions:</h3>
        <ul>
            <li>Enter a document ID and your name</li>
            <li>Click "Connect" to join the collaborative session</li>
            <li>Open this page in multiple tabs or browsers to see real-time collaboration</li>
            <li>Changes are synchronized automatically using YJS and y-websocket</li>
        </ul>
    </div>

    <!-- Load dependencies from CDN -->
    <script src="https://unpkg.com/yjs@^13.0.0/dist/yjs.js"></script>
    <script src="https://unpkg.com/y-websocket@^1.3.0/dist/y-websocket.js"></script>
    <script src="https://unpkg.com/@tiptap/core@^2.0.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@tiptap/starter-kit@^2.0.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@tiptap/extension-collaboration@^2.0.0/dist/index.umd.js"></script>
    <script src="https://unpkg.com/@tiptap/extension-collaboration-cursor@^2.0.0/dist/index.umd.js"></script>

    <script>
        let editor = null;
        let provider = null;
        let doc = null;

        // Set default user ID
        document.getElementById('userId').value = `User-${Math.random().toString(36).substr(2, 5)}`;

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function connectToDocument() {
            const documentId = document.getElementById('documentId').value.trim();
            const userId = document.getElementById('userId').value.trim();

            if (!documentId) {
                alert('Please enter a document ID');
                return;
            }

            if (!userId) {
                alert('Please enter your name');
                return;
            }

            // Disconnect existing connection
            disconnect();

            updateStatus('connecting', 'Connecting...');

            try {
                // Create YJS document
                doc = new Y.Doc();

                // Create WebSocket provider
                const wsUrl = `ws://localhost:3000/yjs?room=${encodeURIComponent(documentId)}&userId=${encodeURIComponent(userId)}`;
                provider = new yWebsocket.WebsocketProvider(wsUrl, documentId, doc);

                // Handle connection events
                provider.on('status', event => {
                    console.log('Provider status:', event.status);
                    if (event.status === 'connected') {
                        updateStatus('connected', `Connected to "${documentId}" as ${userId}`);
                    } else if (event.status === 'disconnected') {
                        updateStatus('disconnected', 'Disconnected');
                    }
                });

                provider.on('sync', isSynced => {
                    console.log('Sync status:', isSynced);
                });

                // Create Tiptap editor with collaboration
                editor = new tiptapCore.Editor({
                    element: document.getElementById('editor'),
                    extensions: [
                        tiptapStarterKit.StarterKit.configure({
                            history: false, // Disable history extension
                        }),
                        tiptapCollaboration.Collaboration.configure({
                            document: doc,
                        }),
                        tiptapCollaborationCursor.CollaborationCursor.configure({
                            provider: provider,
                            user: {
                                name: userId,
                                color: getRandomColor(),
                            },
                        }),
                    ],
                    content: '<p>Start typing to see real-time collaboration in action!</p>',
                });

                console.log('Connected to document:', documentId);

            } catch (error) {
                console.error('Failed to connect:', error);
                updateStatus('disconnected', 'Connection failed');
                alert('Failed to connect: ' + error.message);
            }
        }

        function disconnect() {
            if (editor) {
                editor.destroy();
                editor = null;
            }

            if (provider) {
                provider.destroy();
                provider = null;
            }

            if (doc) {
                doc.destroy();
                doc = null;
            }

            updateStatus('disconnected', 'Disconnected');
            document.getElementById('editor').innerHTML = '';
        }

        function getRandomColor() {
            const colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        // Handle page unload
        window.addEventListener('beforeunload', disconnect);
    </script>
</body>
</html>
