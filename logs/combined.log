{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.439Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.444Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.445Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.446Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.452Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:34:24.452Z"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.200Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.204Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.205Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.205Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.206Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.206Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.214Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"production","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:37:56.215Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"BCol_1xASC5fplh6AAAB","timestamp":"2025-07-04T18:41:55.354Z"}
{"connectionId":"BCol_1xASC5fplh6AAAB","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:41:55.357Z","userId":"test-user-1751654515354"}
{"documentId":"test-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T18:41:55.362Z"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"BCol_1xASC5fplh6AAAB","timestamp":"2025-07-04T18:41:55.366Z","totalConnections":1,"userId":"test-user-1751654515354"}
{"connectionId":"BCol_1xASC5fplh6AAAB","documentId":"test-document","duration":15,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:41:55.372Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"client namespace disconnect","remainingConnections":0,"service":"realtime-yjs-server","socketId":"BCol_1xASC5fplh6AAAB","timestamp":"2025-07-04T18:41:55.372Z","userId":"test-user-1751654515354"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"rfknqaFeZkOSlE8fAAAD","timestamp":"2025-07-04T18:42:08.146Z"}
{"connectionId":"rfknqaFeZkOSlE8fAAAD","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:08.149Z","userId":"user-x637gema3"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"rfknqaFeZkOSlE8fAAAD","timestamp":"2025-07-04T18:42:08.151Z","totalConnections":1,"userId":"user-x637gema3"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"bv2TsdH515RJW0NrAAAF","timestamp":"2025-07-04T18:42:33.546Z"}
{"connectionId":"bv2TsdH515RJW0NrAAAF","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:33.548Z","userId":"user-tws77kcuu"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"bv2TsdH515RJW0NrAAAF","timestamp":"2025-07-04T18:42:33.550Z","totalConnections":2,"userId":"user-tws77kcuu"}
{"connectionId":"bv2TsdH515RJW0NrAAAF","documentId":"test-document","duration":22635,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:56.183Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"bv2TsdH515RJW0NrAAAF","timestamp":"2025-07-04T18:42:56.185Z","userId":"user-tws77kcuu"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"iCYG7-QefPt2ffnLAAAH","timestamp":"2025-07-04T18:42:58.058Z"}
{"connectionId":"iCYG7-QefPt2ffnLAAAH","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:42:58.061Z","userId":"user-nm83ygtbg"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"iCYG7-QefPt2ffnLAAAH","timestamp":"2025-07-04T18:42:58.064Z","totalConnections":2,"userId":"user-nm83ygtbg"}
{"connectionId":"rfknqaFeZkOSlE8fAAAD","documentId":"test-document","duration":92407,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:40.555Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"rfknqaFeZkOSlE8fAAAD","timestamp":"2025-07-04T18:43:40.560Z","userId":"user-x637gema3"}
{"connectionId":"iCYG7-QefPt2ffnLAAAH","documentId":"test-document","duration":42503,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:40.564Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":0,"service":"realtime-yjs-server","socketId":"iCYG7-QefPt2ffnLAAAH","timestamp":"2025-07-04T18:43:40.565Z","userId":"user-nm83ygtbg"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.267Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.271Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.272Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.273Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.273Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.273Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.279Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:47.279Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"EXbzHqsAGIjLdvPbAAAB","timestamp":"2025-07-04T18:43:48.039Z"}
{"connectionId":"EXbzHqsAGIjLdvPbAAAB","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:48.042Z","userId":"user-x637gema3"}
{"documentId":"test-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:48.044Z"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"EXbzHqsAGIjLdvPbAAAB","timestamp":"2025-07-04T18:43:48.046Z","totalConnections":1,"userId":"user-x637gema3"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"cmAERcca4yd_BtW0AAAD","timestamp":"2025-07-04T18:43:49.028Z"}
{"connectionId":"cmAERcca4yd_BtW0AAAD","documentId":"test-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:43:49.029Z","userId":"user-nm83ygtbg"}
{"documentId":"test-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"cmAERcca4yd_BtW0AAAD","timestamp":"2025-07-04T18:43:49.030Z","totalConnections":2,"userId":"user-nm83ygtbg"}
{"connectionId":"EXbzHqsAGIjLdvPbAAAB","documentId":"test-document","duration":50622,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:44:38.663Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"EXbzHqsAGIjLdvPbAAAB","timestamp":"2025-07-04T18:44:38.669Z","userId":"user-x637gema3"}
{"connectionId":"cmAERcca4yd_BtW0AAAD","documentId":"test-document","duration":55741,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:44:44.770Z"}
{"documentId":"test-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":0,"service":"realtime-yjs-server","socketId":"cmAERcca4yd_BtW0AAAD","timestamp":"2025-07-04T18:44:44.770Z","userId":"user-nm83ygtbg"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.887Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.891Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.892Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.892Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.892Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.892Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.899Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:48:02.899Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"lwX1copzd1AS6H_JAAAB","timestamp":"2025-07-04T18:50:46.006Z"}
{"connectionId":"lwX1copzd1AS6H_JAAAB","documentId":"skypack-test","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:50:46.018Z","userId":"skypack-user"}
{"documentId":"skypack-test","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T18:50:46.023Z"}
{"documentId":"skypack-test","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"lwX1copzd1AS6H_JAAAB","timestamp":"2025-07-04T18:50:46.024Z","totalConnections":1,"userId":"skypack-user"}
{"connectionId":"lwX1copzd1AS6H_JAAAB","documentId":"skypack-test","duration":9,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:50:46.027Z"}
{"documentId":"skypack-test","level":"info","message":"Client disconnected","reason":"client namespace disconnect","remainingConnections":0,"service":"realtime-yjs-server","socketId":"lwX1copzd1AS6H_JAAAB","timestamp":"2025-07-04T18:50:46.028Z","userId":"skypack-user"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"NfM6aVTWFxC-8UJqAAAD","timestamp":"2025-07-04T18:51:43.505Z"}
{"connectionId":"NfM6aVTWFxC-8UJqAAAD","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:51:43.507Z","userId":"user-6vsfhf2dc"}
{"documentId":"demo-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T18:51:43.508Z"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"NfM6aVTWFxC-8UJqAAAD","timestamp":"2025-07-04T18:51:43.509Z","totalConnections":1,"userId":"user-6vsfhf2dc"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"Z0g0gBL9wCpUlV2SAAAF","timestamp":"2025-07-04T18:51:52.306Z"}
{"connectionId":"Z0g0gBL9wCpUlV2SAAAF","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:51:52.309Z","userId":"user-twkipmusy"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"Z0g0gBL9wCpUlV2SAAAF","timestamp":"2025-07-04T18:51:52.310Z","totalConnections":2,"userId":"user-twkipmusy"}
{"connectionId":"NfM6aVTWFxC-8UJqAAAD","documentId":"demo-document","duration":105567,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T18:53:29.074Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"client namespace disconnect","remainingConnections":1,"service":"realtime-yjs-server","socketId":"NfM6aVTWFxC-8UJqAAAD","timestamp":"2025-07-04T18:53:29.075Z","userId":"user-6vsfhf2dc"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.064Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.069Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.070Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.070Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.070Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.070Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.075Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:09.076Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"aizQGRhnn-gzb51FAAAB","timestamp":"2025-07-04T18:58:12.015Z"}
{"connectionId":"aizQGRhnn-gzb51FAAAB","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:12.018Z","userId":"user-twkipmusy"}
{"documentId":"demo-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T18:58:12.020Z"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"aizQGRhnn-gzb51FAAAB","timestamp":"2025-07-04T18:58:12.022Z","totalConnections":1,"userId":"user-twkipmusy"}
{"connectionId":"aizQGRhnn-gzb51FAAAB","documentId":"demo-document","duration":129805,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:00:21.823Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":0,"service":"realtime-yjs-server","socketId":"aizQGRhnn-gzb51FAAAB","timestamp":"2025-07-04T19:00:21.824Z","userId":"user-twkipmusy"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"KkD3m1SK9x9EONT5AAAD","timestamp":"2025-07-04T19:00:36.920Z"}
{"connectionId":"KkD3m1SK9x9EONT5AAAD","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:00:36.923Z","userId":"user-jpniavqyg"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"KkD3m1SK9x9EONT5AAAD","timestamp":"2025-07-04T19:00:36.925Z","totalConnections":1,"userId":"user-jpniavqyg"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"zwfx6OA77QexroTPAAAF","timestamp":"2025-07-04T19:00:40.898Z"}
{"connectionId":"zwfx6OA77QexroTPAAAF","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:00:40.902Z","userId":"user-vv6tgqvmh"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"zwfx6OA77QexroTPAAAF","timestamp":"2025-07-04T19:00:40.903Z","totalConnections":2,"userId":"user-vv6tgqvmh"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:30.806Z"}
{"connectionId":"YFK8lAgLoAIvisXLAAAH","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:15:30.809Z","userId":"user-4s50ryneu"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:30.811Z","totalConnections":3,"userId":"user-4s50ryneu"}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.421Z","updateLength":21}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.494Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.498Z","updateLength":19}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.613Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.615Z","updateLength":20}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.738Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.740Z","updateLength":21}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.935Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:44.937Z","updateLength":22}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.050Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.052Z","updateLength":23}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.204Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.206Z","updateLength":24}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.427Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.429Z","updateLength":25}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.703Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.705Z","updateLength":26}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.919Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:45.921Z","updateLength":27}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:46.126Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:46.128Z","updateLength":28}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:46.269Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:46.271Z","updateLength":29}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:46.400Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:15:46.402Z","updateLength":30}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:00.393Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:00.396Z","updateLength":31}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.161Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.164Z","updateLength":32}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.289Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.291Z","updateLength":33}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.467Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.468Z","updateLength":36}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.543Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.544Z","updateLength":37}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.730Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.733Z","updateLength":38}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.814Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.816Z","updateLength":39}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.952Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:01.955Z","updateLength":40}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.062Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.064Z","updateLength":41}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.506Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.509Z","updateLength":42}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.716Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.719Z","updateLength":43}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.819Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.821Z","updateLength":44}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.935Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:02.937Z","updateLength":45}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:03.027Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:03.030Z","updateLength":46}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:03.074Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:03.076Z","updateLength":47}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:03.126Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:03.128Z","updateLength":48}
{"connectionId":"YFK8lAgLoAIvisXLAAAH","documentId":"demo-document","duration":64929,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:16:35.738Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":2,"service":"realtime-yjs-server","socketId":"YFK8lAgLoAIvisXLAAAH","timestamp":"2025-07-04T19:16:35.739Z","userId":"user-4s50ryneu"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:16:37.252Z"}
{"connectionId":"2Vyt3VmxypdYo7QuAAAJ","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:16:37.254Z","userId":"user-qyul14809"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:16:37.255Z","totalConnections":3,"userId":"user-qyul14809"}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.221Z","updateLength":11}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.223Z","updateLength":48}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.332Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.333Z","updateLength":48}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.462Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.465Z","updateLength":49}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.556Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.557Z","updateLength":50}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.766Z","updateLength":10}
{"documentId":"demo-document","level":"info","message":"Received YJS update","service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:03.768Z","updateLength":51}
{"connectionId":"2Vyt3VmxypdYo7QuAAAJ","documentId":"demo-document","duration":43674,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:17:20.928Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":2,"service":"realtime-yjs-server","socketId":"2Vyt3VmxypdYo7QuAAAJ","timestamp":"2025-07-04T19:17:20.928Z","userId":"user-qyul14809"}
{"connectionId":"KkD3m1SK9x9EONT5AAAD","documentId":"demo-document","duration":1005924,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:17:22.847Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"KkD3m1SK9x9EONT5AAAD","timestamp":"2025-07-04T19:17:22.847Z","userId":"user-jpniavqyg"}
{"connectionId":"zwfx6OA77QexroTPAAAF","documentId":"demo-document","duration":1006977,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:17:27.879Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":0,"service":"realtime-yjs-server","socketId":"zwfx6OA77QexroTPAAAF","timestamp":"2025-07-04T19:17:27.879Z","userId":"user-vv6tgqvmh"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"IvxnOwPnS7zALTJpAAAL","timestamp":"2025-07-04T19:17:35.852Z"}
{"connectionId":"IvxnOwPnS7zALTJpAAAL","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:17:35.854Z","userId":"user-qq6vp9bdg"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"IvxnOwPnS7zALTJpAAAL","timestamp":"2025-07-04T19:17:35.856Z","totalConnections":1,"userId":"user-qq6vp9bdg"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"5o8zVeWJfAHz5zfJAAAN","timestamp":"2025-07-04T19:17:55.069Z"}
{"connectionId":"5o8zVeWJfAHz5zfJAAAN","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:17:55.071Z","userId":"user-pou1knxoq"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"5o8zVeWJfAHz5zfJAAAN","timestamp":"2025-07-04T19:17:55.072Z","totalConnections":2,"userId":"user-pou1knxoq"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.786Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.790Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.792Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.792Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.792Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.792Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.798Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:41.799Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"iL6480u74ZRN37M5AAAB","timestamp":"2025-07-04T19:22:44.003Z"}
{"connectionId":"iL6480u74ZRN37M5AAAB","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:44.006Z","userId":"user-pou1knxoq"}
{"documentId":"demo-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:44.010Z"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"iL6480u74ZRN37M5AAAB","timestamp":"2025-07-04T19:22:44.012Z","totalConnections":1,"userId":"user-pou1knxoq"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"Vnu-ikczt9VRe1pzAAAD","timestamp":"2025-07-04T19:22:47.203Z"}
{"connectionId":"Vnu-ikczt9VRe1pzAAAD","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:22:47.204Z","userId":"user-qq6vp9bdg"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"Vnu-ikczt9VRe1pzAAAD","timestamp":"2025-07-04T19:22:47.205Z","totalConnections":2,"userId":"user-qq6vp9bdg"}
{"level":"info","message":"Starting Realtime YJS Server...","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.419Z"}
{"level":"info","message":"Express server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.423Z"}
{"level":"info","message":"Socket.IO server initialized","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.425Z"}
{"level":"info","message":"YJS Service initializing...","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.425Z"}
{"level":"info","message":"YJS Service initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.426Z"}
{"level":"info","message":"All components initialized successfully","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.426Z"}
{"level":"info","message":"Server started on 0.0.0.0:3000","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.431Z"}
{"host":"0.0.0.0","level":"info","message":"Realtime YJS Server is running","nodeEnv":"development","port":"3000","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:35.432Z"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"PZbQ0zR8ogptuLsuAAAB","timestamp":"2025-07-04T19:25:36.976Z"}
{"connectionId":"PZbQ0zR8ogptuLsuAAAB","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:36.979Z","userId":"user-qq6vp9bdg"}
{"documentId":"demo-document","level":"info","message":"Document created","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:36.981Z"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"PZbQ0zR8ogptuLsuAAAB","timestamp":"2025-07-04T19:25:36.983Z","totalConnections":1,"userId":"user-qq6vp9bdg"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"vVSsfumTwsJ38wWUAAAD","timestamp":"2025-07-04T19:25:37.964Z"}
{"connectionId":"vVSsfumTwsJ38wWUAAAD","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:25:37.966Z","userId":"user-pou1knxoq"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"vVSsfumTwsJ38wWUAAAD","timestamp":"2025-07-04T19:25:37.967Z","totalConnections":2,"userId":"user-pou1knxoq"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"ppWLWBPtSL0guzcTAAAF","timestamp":"2025-07-04T19:27:11.752Z"}
{"connectionId":"ppWLWBPtSL0guzcTAAAF","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:27:11.754Z","userId":"user-o52kxwie3"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"ppWLWBPtSL0guzcTAAAF","timestamp":"2025-07-04T19:27:11.755Z","totalConnections":3,"userId":"user-o52kxwie3"}
{"connectionId":"PZbQ0zR8ogptuLsuAAAB","documentId":"demo-document","duration":101353,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:27:18.332Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":2,"service":"realtime-yjs-server","socketId":"PZbQ0zR8ogptuLsuAAAB","timestamp":"2025-07-04T19:27:18.335Z","userId":"user-qq6vp9bdg"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"YWGloh5hNTDi-8_OAAAH","timestamp":"2025-07-04T19:27:24.084Z"}
{"connectionId":"YWGloh5hNTDi-8_OAAAH","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:27:24.087Z","userId":"user-itb9wzcaq"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"YWGloh5hNTDi-8_OAAAH","timestamp":"2025-07-04T19:27:24.088Z","totalConnections":3,"userId":"user-itb9wzcaq"}
{"connectionId":"vVSsfumTwsJ38wWUAAAD","documentId":"demo-document","duration":111014,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:27:28.980Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":2,"service":"realtime-yjs-server","socketId":"vVSsfumTwsJ38wWUAAAD","timestamp":"2025-07-04T19:27:28.981Z","userId":"user-pou1knxoq"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"9nyWU97jTaBd6umoAAAJ","timestamp":"2025-07-04T19:27:35.248Z"}
{"connectionId":"9nyWU97jTaBd6umoAAAJ","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:27:35.250Z","userId":"user-nvs0b4zse"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"9nyWU97jTaBd6umoAAAJ","timestamp":"2025-07-04T19:27:35.251Z","totalConnections":3,"userId":"user-nvs0b4zse"}
{"connectionId":"ppWLWBPtSL0guzcTAAAF","documentId":"demo-document","duration":27354,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:27:39.108Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":2,"service":"realtime-yjs-server","socketId":"ppWLWBPtSL0guzcTAAAF","timestamp":"2025-07-04T19:27:39.109Z","userId":"user-o52kxwie3"}
{"connectionId":"YWGloh5hNTDi-8_OAAAH","documentId":"demo-document","duration":133931,"level":"info","message":"Connection removed","service":"realtime-yjs-server","timestamp":"2025-07-04T19:29:38.017Z"}
{"documentId":"demo-document","level":"info","message":"Client disconnected","reason":"transport close","remainingConnections":1,"service":"realtime-yjs-server","socketId":"YWGloh5hNTDi-8_OAAAH","timestamp":"2025-07-04T19:29:38.018Z","userId":"user-itb9wzcaq"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"pmIlLNJ6ogd1l5zeAAAL","timestamp":"2025-07-04T19:29:42.713Z"}
{"connectionId":"pmIlLNJ6ogd1l5zeAAAL","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:29:42.715Z","userId":"user-vdwdx2f5c"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"pmIlLNJ6ogd1l5zeAAAL","timestamp":"2025-07-04T19:29:42.716Z","totalConnections":2,"userId":"user-vdwdx2f5c"}
{"level":"info","message":"New socket connection","service":"realtime-yjs-server","socketId":"vIuzFFv8xgD32iXaAAAN","timestamp":"2025-07-04T19:30:53.784Z"}
{"connectionId":"vIuzFFv8xgD32iXaAAAN","documentId":"demo-document","level":"info","message":"Connection added","service":"realtime-yjs-server","timestamp":"2025-07-04T19:30:53.787Z","userId":"user-uearq31vj"}
{"documentId":"demo-document","level":"info","message":"Client joined document","service":"realtime-yjs-server","socketId":"vIuzFFv8xgD32iXaAAAN","timestamp":"2025-07-04T19:30:53.790Z","totalConnections":3,"userId":"user-uearq31vj"}
