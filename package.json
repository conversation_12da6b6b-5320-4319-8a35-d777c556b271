{"name": "realtime-yjs-server", "version": "1.0.0", "description": "A real-time collaborative server using YJS and y-websocket with SOLID principles, optimized for Tiptap integration", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "docker:build": "docker build -t realtime-yjs-server .", "docker:run": "docker run -p 3000:3000 realtime-yjs-server"}, "keywords": ["yjs", "y-websocket", "realtime", "collaboration", "websocket", "nodejs", "tiptap", "crdt"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "winston": "^3.11.0", "ws": "^8.18.3", "y-leveldb": "^0.2.0", "y-websocket": "^3.0.0", "yjs": "^13.6.10"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}