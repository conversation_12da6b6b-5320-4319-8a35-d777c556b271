export const structSkipRefNumber: 10;
/**
 * @private
 */
export class Skip extends AbstractStruct {
    delete(): void;
    /**
     * @param {Skip} right
     * @return {boolean}
     */
    mergeWith(right: Skip): boolean;
    /**
     * @param {UpdateEncoderV1 | UpdateEncoderV2} encoder
     * @param {number} offset
     */
    write(encoder: UpdateEncoderV1 | UpdateEncoderV2, offset: number): void;
    /**
     * @param {Transaction} transaction
     * @param {StructStore} store
     * @return {null | number}
     */
    getMissing(transaction: Transaction, store: StructStore): null | number;
}
import { AbstractStruct } from "./AbstractStruct.js";
import { UpdateEncoderV1 } from "../utils/UpdateEncoder.js";
import { UpdateEncoderV2 } from "../utils/UpdateEncoder.js";
import { Transaction } from "../utils/Transaction.js";
import { StructStore } from "../utils/StructStore.js";
//# sourceMappingURL=Skip.d.ts.map