{"version": 3, "file": "y-leveldb.d.ts", "sourceRoot": "", "sources": ["../../src/y-leveldb.js"], "names": [], "mappings": "AAUA,kCAAmC,GAAG,CAAA;AA2B/B,8CAHI,QAAQ,CAAC,OAAO,OAChB,MAAM,QAMhB;AAYM,6CAHI,QAAQ,CAAC,OAAO,GACf,MAAM,CAUjB;;;;IAMmD,qBAA9B,KAAK,CAAC,MAAM,GAAC,MAAM,CAAC,uBAevC;IACsC,qBAAnB,UAAU,uBAc7B;;AA8CI,wCAJI,aAAa,QACb,OAAO,gBAAgB,EAAE,uBAAuB,CAAC,GAAG,EAAE,UAAU,CAAC,GAChE,OAAO,CAAC,KAAK,CAAC;IAAE,GAAG,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC,CAY5D;AASK,qCAJI,aAAa,QACb,OAAO,gBAAgB,EAAE,uBAAuB,CAAC,GAAG,EAAE,UAAU,CAAC,GAChE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAYhC;AASK,uCAJI,aAAa,QACb,OAAO,gBAAgB,EAAE,uBAAuB,CAAC,MAAM,EAAE,UAAU,CAAC,GACnE,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAYpC;AAUK,oCALI,GAAG,WACH,MAAM,SACN,GAAG,GACF,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAMpC;AAUK,2CALI,GAAG,WACH,MAAM,SACN,GAAG,GACF,OAAO,CAAC,KAAK,CAAC;IAAC,GAAG,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,UAAU,CAAA;CAAE,CAAC,CAAC,CAM3D;AAWK,wCANI,GAAG,WACH,MAAM,SACN,GAAG,GACF,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAOhC;AAOK,mCAFI,aAAa,qBAKtB;AAOK,+BAFI,aAAa;SA5GS,MAAM;WAAS,UAAU;KAiHxD;AAOK,0CAJI,GAAG,WACH,MAAM,GACL,OAAO,CAAC,MAAM,CAAC,CAQzB;AAgKF;IACE;;;;;OAKG;IACH,sBALW,MAAM,4BAEd;QAAmB,KAAK,GAAhB,GAAG;QACW,YAAY;KACpC,EAkCA;IA5BC,iBAA2B;IAC3B;;;;;;;;;;;OAWG;IACH,YALa,CAAC,KAEH,CAAS,IAAG,EAAH,GAAG,KAAE,OAAO,CAAC,CAAC,CAAC,KACvB,OAAO,CAAC,CAAC,CAAC,CAgBrB;IAGH;;OAEG;IACH,uBAFW,MAAM,iBAQhB;IAED;;;OAGG;IACH,iBAHW,MAAM,GACL,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAgBzB;IAED;;;OAGG;IACH,wBAHW,MAAM,GACL,OAAO,CAAC,UAAU,CAAC,CAmB9B;IAED;;;;OAIG;IACH,qBAJW,MAAM,UACN,UAAU,GACT,OAAO,CAAC,MAAM,CAAC,CAI1B;IAED;;;OAGG;IACH,iBAHW,MAAM,eACN,UAAU,wCAKpB;IAED;;;OAGG;IACH,uBAHW,MAAM,GACL,OAAO,CAAC,IAAI,CAAC,CAOxB;IAED;;;;;OAKG;IACH,iBALW,MAAM,WACN,MAAM,SACN,GAAG,GACF,OAAO,CAAC,IAAI,CAAC,CAIxB;IAED;;;;OAIG;IACH,iBAJW,MAAM,WACN,MAAM,GACL,OAAO,CAAC,GAAG,CAAC,CAIvB;IAED;;;;OAIG;IACH,iBAJW,MAAM,WACN,MAAM,GACL,OAAO,CAAC,GAAG,CAAC,CAUvB;IAED;;OAEG;IACH,kBAFY,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAOjC;IAED;;OAEG;IACH,yBAFY,OAAO,CAAC,KAAK,CAAC;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,EAAE,EAAE,UAAU,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC,CAU1E;IAED;;;OAGG;IACH,kBAHW,MAAM,GACL,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAcpC;IAED;;;;OAIG;IACH,WAFY,OAAO,CAAC,IAAI,CAAC,CAIxB;IAED;;OAEG;IACH,yBAEC;CACF;4BAlnBY,OAAO,gBAAgB,EAAE,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,SAAO,MAAM,CAAC,EAAE,UAAU,CAAC;qBAG7E,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;0BAlBxE,kBAAkB;0BAClB,kBAAkB;uBAMrB,QAAQ;mBARZ,KAAK"}