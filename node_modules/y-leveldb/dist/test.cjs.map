{"version": 3, "file": "test.cjs", "sources": ["../src/y-leveldb.js", "../tests/y-leveldb.tests.js", "../tests/index.js"], "sourcesContent": ["import * as Y from 'yjs'\nimport * as encoding from 'lib0/encoding.js'\nimport * as decoding from 'lib0/decoding.js'\nimport * as binary from 'lib0/binary.js'\nimport * as promise from 'lib0/promise.js'\nimport * as buffer from 'lib0/buffer.js'\nimport { Level as DefaultLevel } from 'level'\nimport { EntryStream, KeyStream, ValueStream } from 'level-read-stream'\nimport { <PERSON>uffer } from 'buffer'\n\nexport const PREFERRED_TRIM_SIZE = 500\n\nconst YEncodingString = 0\nconst YEncodingUint32 = 1\n\n/**\n * @typedef {import('abstract-level').AbstractLevel<any, Array<String|number>, Uint8Array>} AbstractLevel\n */\n/**\n * @typedef {['v1', string, 'update', number] | ['v1', string, 'meta', string] | ['v1_sv', number]} DocKey\n */\n\nconst valueEncoding = {\n  buffer: true,\n  type: 'y-value',\n  encode: /** @param {any} data */ data => data,\n  decode: /** @param {any} data */ data => data\n}\n\n/**\n * Write two bytes as an unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @function\n * @param {encoding.Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nexport const writeUint32BigEndian = (encoder, num) => {\n  for (let i = 3; i >= 0; i--) {\n    encoding.write(encoder, (num >>> (8 * i)) & binary.BITS8)\n  }\n}\n\n/**\n * Read 4 bytes as unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @todo use lib0/decoding instead\n *\n * @function\n * @param {decoding.Decoder} decoder\n * @return {number} An unsigned integer.\n */\nexport const readUint32BigEndian = decoder => {\n  const uint =\n    (decoder.arr[decoder.pos + 3] +\n    (decoder.arr[decoder.pos + 2] << 8) +\n    (decoder.arr[decoder.pos + 1] << 16) +\n    (decoder.arr[decoder.pos] << 24)) >>> 0\n  decoder.pos += 4\n  return uint\n}\n\nexport const keyEncoding = {\n  buffer: true,\n  type: 'y-keys',\n  /* istanbul ignore next */\n  encode: /** @param {Array<string|number>} arr */  arr => {\n    const encoder = encoding.createEncoder()\n    for (let i = 0; i < arr.length; i++) {\n      const v = arr[i]\n      if (typeof v === 'string') {\n        encoding.writeUint8(encoder, YEncodingString)\n        encoding.writeVarString(encoder, v)\n      } else /* istanbul ignore else */ if (typeof v === 'number') {\n        encoding.writeUint8(encoder, YEncodingUint32)\n        writeUint32BigEndian(encoder, v)\n      } else {\n        throw new Error('Unexpected key value')\n      }\n    }\n    return Buffer.from(encoding.toUint8Array(encoder))\n  },\n  decode: /** @param {Uint8Array} buf */ buf => {\n    const decoder = decoding.createDecoder(buf)\n    const key = []\n    while (decoding.hasContent(decoder)) {\n      switch (decoding.readUint8(decoder)) {\n        case YEncodingString:\n          key.push(decoding.readVarString(decoder))\n          break\n        case YEncodingUint32:\n          key.push(readUint32BigEndian(decoder))\n          break\n      }\n    }\n    return key\n  }\n}\n\n/**\n * level returns an error if a value is not found.\n *\n * This helper method for level returns `null` instead if the key is not found.\n *\n * @param {AbstractLevel} db\n * @param {any} key\n * @return {Promise<Uint8Array | undefined>}\n */\nconst levelGet = async (db, key) => {\n  let res\n  try {\n    res = await db.get(key)\n  } catch (err) {\n    /* istanbul ignore else */\n    if (/** @type {any} */ (err).notFound) {\n      return\n    } else {\n      throw err\n    }\n  }\n  return res\n}\n\n/**\n * Level expects a Buffer, but in Yjs we typically work with Uint8Arrays.\n *\n * Since Level thinks that these are two entirely different things,\n * we transform the Uint8array to a Buffer before storing it.\n *\n * @param {any} db\n * @param {any} key\n * @param {Uint8Array} val\n */\nconst levelPut = async (db, key, val) => db.put(key, Buffer.from(val))\n\n/**\n * A \"bulkier\" implementation of level streams. Returns the result in one flush.\n *\n * @param {AbstractLevel} db\n * @param {import('abstract-level').AbstractIteratorOptions<any, Uint8Array>} opts\n * @return {Promise<Array<{ key: DocKey, value: Uint8Array }>>}\n */\nexport const getLevelBulkEntries = (db, opts) => promise.create((resolve, reject) => {\n  /**\n   * @type {Array<any>} result\n   */\n  const result = []\n  new EntryStream(db, opts).on('data', data => {\n    result.push(data)\n  }).on('end', () => {\n    resolve(result)\n  }).on('error', reject)\n})\n\n/**\n * A \"bulkier\" implementation of level streams. Returns the result in one flush.\n *\n * @param {AbstractLevel} db\n * @param {import('abstract-level').AbstractIteratorOptions<any, Uint8Array>} opts\n * @return {Promise<Array<DocKey>>}\n */\nexport const getLevelBulkKeys = (db, opts) => promise.create((resolve, reject) => {\n  /**\n   * @type {Array<any>} result\n   */\n  const result = []\n  new KeyStream(db, opts).on('data', data => {\n    result.push(data)\n  }).on('end', () => {\n    resolve(result)\n  }).on('error', reject)\n})\n\n/**\n * A \"bulkier\" implementation of level streams. Returns the result in one flush.\n *\n * @param {AbstractLevel} db\n * @param {import('abstract-level').AbstractIteratorOptions<DocKey, Uint8Array>} opts\n * @return {Promise<Array<Uint8Array>>}\n */\nexport const getLevelBulkValues = (db, opts) => promise.create((resolve, reject) => {\n  /**\n   * @type {Array<any>} result\n   */\n  const result = []\n  new ValueStream(db, opts).on('data', data => {\n    result.push(data)\n  }).on('end', () => {\n    resolve(result)\n  }).on('error', reject)\n})\n\n/**\n * Get all document updates for a specific document.\n *\n * @param {any} db\n * @param {string} docName\n * @param {any} [opts]\n * @return {Promise<Array<Uint8Array>>}\n */\nexport const getLevelUpdates = (db, docName, opts = {}) => getLevelBulkValues(db, {\n  gte: createDocumentUpdateKey(docName, 0),\n  lt: createDocumentUpdateKey(docName, binary.BITS32),\n  ...opts\n})\n\n/**\n * Get all document updates for a specific document.\n *\n * @param {any} db\n * @param {string} docName\n * @param {any} [opts]\n * @return {Promise<Array<{key: DocKey, value: Uint8Array }>>}\n */\nexport const getLevelUpdatesEntries = (db, docName, opts = {}) => getLevelBulkEntries(db, {\n  gte: createDocumentUpdateKey(docName, 0),\n  lt: createDocumentUpdateKey(docName, binary.BITS32),\n  ...opts\n})\n\n/**\n * Get all document updates for a specific document.\n *\n * @param {any} db\n * @param {string} docName\n * @param {any} opts\n * @return {Promise<Array<DocKey>>}\n */\n/* istanbul ignore next */\nexport const getLevelUpdatesKeys = (db, docName, opts = {}) => getLevelBulkKeys(db, {\n  gte: createDocumentUpdateKey(docName, 0),\n  lt: createDocumentUpdateKey(docName, binary.BITS32),\n  ...opts\n})\n\n/**\n * Get all document updates for a specific document.\n *\n * @param {AbstractLevel} db\n */\nexport const getAllDocsKeys = (db) => getLevelBulkKeys(db, {\n  gte: ['v1_sv'],\n  lt: ['v1_sw']\n})\n\n/**\n * Get all document updates for a specific document.\n *\n * @param {AbstractLevel} db\n */\nexport const getAllDocs = (db) => getLevelBulkEntries(db, {\n  gte: ['v1_sv'],\n  lt: ['v1_sw']\n})\n\n/**\n * @param {any} db\n * @param {string} docName\n * @return {Promise<number>} Returns -1 if this document doesn't exist yet\n */\nexport const getCurrentUpdateClock = (db, docName) => getLevelUpdatesKeys(db, docName, { reverse: true, limit: 1 }).then(entries => {\n  if (entries.length === 0) {\n    return -1\n  } else {\n    return /** @type {number} */ (entries[0][3])\n  }\n})\n\n/**\n * @param {any} db\n * @param {Array<string|number>} gte Greater than or equal\n * @param {Array<string|number>} lt lower than (not equal)\n * @return {Promise<void>}\n */\nconst clearRange = async (db, gte, lt) => {\n  /* istanbul ignore else */\n  if (db.supports.clear) {\n    await db.clear({ gte, lt })\n  } else {\n    const keys = await getLevelBulkKeys(db, { gte, lt })\n    const ops = keys.map(key => ({ type: 'del', key }))\n    await db.batch(ops)\n  }\n}\n\n/**\n * @param {any} db\n * @param {string} docName\n * @param {number} from Greater than or equal\n * @param {number} to lower than (not equal)\n * @return {Promise<void>}\n */\nconst clearUpdatesRange = async (db, docName, from, to) => clearRange(db, createDocumentUpdateKey(docName, from), createDocumentUpdateKey(docName, to))\n\n/**\n * Create a unique key for a update message.\n * We encode the result using `keyEncoding` which expects an array.\n *\n * @param {string} docName\n * @param {number} clock must be unique\n * @return {DocKey}\n */\nconst createDocumentUpdateKey = (docName, clock) => ['v1', docName, 'update', clock]\n\n/**\n * @param {string} docName\n * @param {string} metaKey\n * @return {any}\n */\nconst createDocumentMetaKey = (docName, metaKey) => ['v1', docName, 'meta', metaKey]\n\n/**\n * @param {string} docName\n * @return {any}\n */\nconst createDocumentMetaEndKey = (docName) => ['v1', docName, 'metb'] // simple trick\n\n/**\n * We have a separate state vector key so we can iterate efficiently over all documents\n * @param {string} docName\n */\nconst createDocumentStateVectorKey = (docName) => ['v1_sv', docName]\n\n/**\n * @param {string} docName\n */\nconst createDocumentFirstKey = (docName) => ['v1', docName]\n\n/**\n * We use this key as the upper limit of all keys that can be written.\n * Make sure that all document keys are smaller! Strings are encoded using varLength string encoding,\n * so we need to make sure that this key has the biggest size!\n *\n * @param {string} docName\n */\nconst createDocumentLastKey = (docName) => ['v1', docName, 'zzzzzzz']\n\n// const emptyStateVector = (() => Y.encodeStateVector(new Y.Doc()))()\n\n/**\n * For now this is a helper method that creates a Y.Doc and then re-encodes a document update.\n * In the future this will be handled by Yjs without creating a Y.Doc (constant memory consumption).\n *\n * @param {Array<Uint8Array>} updates\n * @return {{update:Uint8Array, sv: Uint8Array}}\n */\nconst mergeUpdates = (updates) => {\n  const ydoc = new Y.Doc()\n  ydoc.transact(() => {\n    for (let i = 0; i < updates.length; i++) {\n      Y.applyUpdate(ydoc, updates[i])\n    }\n  })\n  return { update: Y.encodeStateAsUpdate(ydoc), sv: Y.encodeStateVector(ydoc) }\n}\n\n/**\n * @param {any} db\n * @param {string} docName\n * @param {Uint8Array} sv state vector\n * @param {number} clock current clock of the document so we can determine when this statevector was created\n */\nconst writeStateVector = async (db, docName, sv, clock) => {\n  const encoder = encoding.createEncoder()\n  encoding.writeVarUint(encoder, clock)\n  encoding.writeVarUint8Array(encoder, sv)\n  await levelPut(db, createDocumentStateVectorKey(docName), encoding.toUint8Array(encoder))\n}\n\n/**\n * @param {Uint8Array} buf\n * @return {{ sv: Uint8Array, clock: number }}\n */\nconst decodeLeveldbStateVector = buf => {\n  const decoder = decoding.createDecoder(buf)\n  const clock = decoding.readVarUint(decoder)\n  const sv = decoding.readVarUint8Array(decoder)\n  return { sv, clock }\n}\n\n/**\n * @param {any} db\n * @param {string} docName\n */\nconst readStateVector = async (db, docName) => {\n  const buf = await levelGet(db, createDocumentStateVectorKey(docName))\n  if (buf == null) {\n    // no state vector created yet or no document exists\n    return { sv: null, clock: -1 }\n  }\n  return decodeLeveldbStateVector(buf)\n}\n\n/**\n * @param {any} db\n * @param {string} docName\n * @param {Uint8Array} stateAsUpdate\n * @param {Uint8Array} stateVector\n * @return {Promise<number>} returns the clock of the flushed doc\n */\nconst flushDocument = async (db, docName, stateAsUpdate, stateVector) => {\n  const clock = await storeUpdate(db, docName, stateAsUpdate)\n  await writeStateVector(db, docName, stateVector, clock)\n  await clearUpdatesRange(db, docName, 0, clock) // intentionally not waiting for the promise to resolve!\n  return clock\n}\n\n/**\n * @param {any} db\n * @param {string} docName\n * @param {Uint8Array} update\n * @return {Promise<number>} Returns the clock of the stored update\n */\nconst storeUpdate = async (db, docName, update) => {\n  const clock = await getCurrentUpdateClock(db, docName)\n  if (clock === -1) {\n    // make sure that a state vector is aways written, so we can search for available documents\n    const ydoc = new Y.Doc()\n    Y.applyUpdate(ydoc, update)\n    const sv = Y.encodeStateVector(ydoc)\n    await writeStateVector(db, docName, sv, 0)\n  }\n  await levelPut(db, createDocumentUpdateKey(docName, clock + 1), update)\n  return clock + 1\n}\n\nexport class LeveldbPersistence {\n  /**\n   * @param {string} location\n   * @param {object} opts\n   * @param {any} [opts.Level] Level-compatible adapter. E.g. leveldown, level-rem, level-indexeddb. Defaults to `level`\n   * @param {object} [opts.levelOptions] Options that are passed down to the level instance\n   */\n  constructor (location, /* istanbul ignore next */ { Level = DefaultLevel, levelOptions = {} } = {}) {\n    /**\n     * @type {import('abstract-level').AbstractLevel<any>}\n     */\n    const db = new Level(location, { ...levelOptions, valueEncoding, keyEncoding })\n    this.tr = promise.resolve()\n    /**\n     * Execute an transaction on a database. This will ensure that other processes are currently not writing.\n     *\n     * This is a private method and might change in the future.\n     *\n     * @todo only transact on the same room-name. Allow for concurrency of different rooms.\n     *\n     * @template T\n     *\n     * @param {function(any):Promise<T>} f A transaction that receives the db object\n     * @return {Promise<T>}\n     */\n    this._transact = f => {\n      const currTr = this.tr\n      this.tr = (async () => {\n        await currTr\n        let res = /** @type {any} */ (null)\n        try {\n          res = await f(db)\n        } catch (err) {\n          /* istanbul ignore next */\n          console.warn('Error during y-leveldb transaction', err)\n        }\n        return res\n      })()\n      return this.tr\n    }\n  }\n\n  /**\n   * @param {string} docName\n   */\n  flushDocument (docName) {\n    return this._transact(async db => {\n      const updates = await getLevelUpdates(db, docName)\n      const { update, sv } = mergeUpdates(updates)\n      await flushDocument(db, docName, update, sv)\n    })\n  }\n\n  /**\n   * @param {string} docName\n   * @return {Promise<Y.Doc>}\n   */\n  getYDoc (docName) {\n    return this._transact(async db => {\n      const updates = await getLevelUpdates(db, docName)\n      const ydoc = new Y.Doc()\n      ydoc.transact(() => {\n        for (let i = 0; i < updates.length; i++) {\n          Y.applyUpdate(ydoc, updates[i])\n        }\n      })\n      if (updates.length > PREFERRED_TRIM_SIZE) {\n        await flushDocument(db, docName, Y.encodeStateAsUpdate(ydoc), Y.encodeStateVector(ydoc))\n      }\n      return ydoc\n    })\n  }\n\n  /**\n   * @param {string} docName\n   * @return {Promise<Uint8Array>}\n   */\n  getStateVector (docName) {\n    return this._transact(async db => {\n      const { clock, sv } = await readStateVector(db, docName)\n      let curClock = -1\n      if (sv !== null) {\n        curClock = await getCurrentUpdateClock(db, docName)\n      }\n      if (sv !== null && clock === curClock) {\n        return sv\n      } else {\n        // current state vector is outdated\n        const updates = await getLevelUpdates(db, docName)\n        const { update, sv } = mergeUpdates(updates)\n        await flushDocument(db, docName, update, sv)\n        return sv\n      }\n    })\n  }\n\n  /**\n   * @param {string} docName\n   * @param {Uint8Array} update\n   * @return {Promise<number>} Returns the clock of the stored update\n   */\n  storeUpdate (docName, update) {\n    return this._transact(db => storeUpdate(db, docName, update))\n  }\n\n  /**\n   * @param {string} docName\n   * @param {Uint8Array} stateVector\n   */\n  async getDiff (docName, stateVector) {\n    const ydoc = await this.getYDoc(docName)\n    return Y.encodeStateAsUpdate(ydoc, stateVector)\n  }\n\n  /**\n   * @param {string} docName\n   * @return {Promise<void>}\n   */\n  clearDocument (docName) {\n    return this._transact(async db => {\n      await db.del(createDocumentStateVectorKey(docName))\n      await clearRange(db, createDocumentFirstKey(docName), createDocumentLastKey(docName))\n    })\n  }\n\n  /**\n   * @param {string} docName\n   * @param {string} metaKey\n   * @param {any} value\n   * @return {Promise<void>}\n   */\n  setMeta (docName, metaKey, value) {\n    return this._transact(db => levelPut(db, createDocumentMetaKey(docName, metaKey), buffer.encodeAny(value)))\n  }\n\n  /**\n   * @param {string} docName\n   * @param {string} metaKey\n   * @return {Promise<any>}\n   */\n  delMeta (docName, metaKey) {\n    return this._transact(db => db.del(createDocumentMetaKey(docName, metaKey)))\n  }\n\n  /**\n   * @param {string} docName\n   * @param {string} metaKey\n   * @return {Promise<any>}\n   */\n  getMeta (docName, metaKey) {\n    return this._transact(async db => {\n      const res = await levelGet(db, createDocumentMetaKey(docName, metaKey))\n      if (res == null) {\n        return\n      }\n      return buffer.decodeAny(res)\n    })\n  }\n\n  /**\n   * @return {Promise<Array<string>>}\n   */\n  getAllDocNames () {\n    return this._transact(async db => {\n      const docKeys = await getAllDocsKeys(db)\n      return docKeys.map(key => /** @type {string} */ (key[1]))\n    })\n  }\n\n  /**\n   * @return {Promise<Array<{ name: string, sv: Uint8Array, clock: number }>>}\n   */\n  getAllDocStateVectors () {\n    return this._transact(async db => {\n      const docs = await getAllDocs(db)\n      return docs.map(doc => {\n        const { sv, clock } = decodeLeveldbStateVector(doc.value)\n        return { name: /** @type {string} */ (doc.key[1]), sv, clock }\n      })\n    })\n  }\n\n  /**\n   * @param {string} docName\n   * @return {Promise<Map<string, any>>}\n   */\n  getMetas (docName) {\n    return this._transact(async db => {\n      const data = await getLevelBulkEntries(db, {\n        gte: createDocumentMetaKey(docName, ''),\n        lt: createDocumentMetaEndKey(docName),\n        keys: true,\n        values: true\n      })\n      const metas = new Map()\n      data.forEach(v => { metas.set(v.key[3], buffer.decodeAny(v.value)) })\n      return metas\n    })\n  }\n\n  /**\n   * Close connection to a leveldb database and discard all state and bindings\n   *\n   * @return {Promise<void>}\n   */\n  destroy () {\n    return this._transact(db => db.close())\n  }\n\n  /**\n   * Delete all data in database.\n   */\n  clearAll () {\n    return this._transact(async db => db.clear())\n  }\n}\n", "import * as Y from 'yjs'\nimport { PREFERRED_TRIM_SIZE, LeveldbPersistence, getLevelBulkEntries, getLevelUpdatesEntries } from '../src/y-leveldb.js'\nimport * as t from 'lib0/testing.js'\nimport * as decoding from 'lib0/decoding.js'\n\n// When changing this, also make sure to change the file in gitignore\nconst storageName = 'tmp-leveldb-storage'\n\n/**\n * Read state vector from Decoder and return as Map. This is a helper method that will be exported by <PERSON>j<PERSON> directly.\n *\n * @param {decoding.Decoder} decoder\n * @return {Map<number,number>} Maps `client` to the number next expected `clock` from that client.\n *\n * @function\n */\nconst readStateVector = decoder => {\n  const ss = new Map()\n  const ssLength = decoding.readVarUint(decoder)\n  for (let i = 0; i < ssLength; i++) {\n    const client = decoding.readVarUint(decoder)\n    const clock = decoding.readVarUint(decoder)\n    ss.set(client, clock)\n  }\n  return ss\n}\n\n/**\n * Read decodedState and return State as Map.\n *\n * @param {Uint8Array} decodedState\n * @return {Map<number,number>} Maps `client` to the number next expected `clock` from that client.\n *\n * @function\n */\nconst decodeStateVector = decodedState => readStateVector(decoding.createDecoder(decodedState))\n\n/**\n * Flushes all updates to ldb and delets items from updates array.\n *\n * @param {LeveldbPersistence} ldb\n * @param {string} docName\n * @param {Array<Uint8Array>} updates\n */\nconst flushUpdatesHelper = (ldb, docName, updates) =>\n  Promise.all(updates.splice(0).map(update => ldb.storeUpdate(docName, update)))\n\n/**\n * @param {t.TestCase} tc\n */\nexport const testLeveldbUpdateStorage = async tc => {\n  const docName = tc.testName\n  const ydoc1 = new Y.Doc()\n  ydoc1.clientID = 0 // so we can check the state vector\n  const leveldbPersistence = new LeveldbPersistence(storageName)\n  // clear all data, so we can check allData later\n  await leveldbPersistence._transact(async db => db.clear())\n  t.compareArrays([], await leveldbPersistence.getAllDocNames())\n\n  /**\n   * @type {Array<Uint8Array>}\n   */\n  const updates = []\n\n  ydoc1.on('update', update => {\n    updates.push(update)\n  })\n\n  ydoc1.getArray('arr').insert(0, [1])\n  ydoc1.getArray('arr').insert(0, [2])\n\n  await flushUpdatesHelper(leveldbPersistence, docName, updates)\n\n  const encodedSv = await leveldbPersistence.getStateVector(docName)\n  const sv = decodeStateVector(encodedSv)\n  t.assert(sv.size === 1)\n  t.assert(sv.get(0) === 2)\n\n  const ydoc2 = await leveldbPersistence.getYDoc(docName)\n  t.compareArrays(ydoc2.getArray('arr').toArray(), [2, 1])\n\n  const allData = await leveldbPersistence._transact(async db => getLevelBulkEntries(db, { gte: ['v1'], lt: ['v2'] }))\n  t.assert(allData.length > 0, 'some data exists')\n\n  t.compareArrays([docName], await leveldbPersistence.getAllDocNames())\n  await leveldbPersistence.clearDocument(docName)\n  t.compareArrays([], await leveldbPersistence.getAllDocNames())\n  const allData2 = await leveldbPersistence._transact(async db => getLevelBulkEntries(db, { gte: ['v1'], lt: ['v2'] }))\n  console.log(allData2)\n  t.assert(allData2.length === 0, 'really deleted all data')\n\n  await leveldbPersistence.destroy()\n}\n\n/**\n * @param {t.TestCase} tc\n */\nexport const testEncodeManyUpdates = async tc => {\n  const N = PREFERRED_TRIM_SIZE * 7\n  const docName = tc.testName\n  const ydoc1 = new Y.Doc()\n  ydoc1.clientID = 0 // so we can check the state vector\n  const leveldbPersistence = new LeveldbPersistence(storageName)\n  await leveldbPersistence.clearDocument(docName)\n\n  /**\n   * @type {Array<Uint8Array>}\n   */\n  const updates = []\n\n  ydoc1.on('update', update => {\n    updates.push(update)\n  })\n  await flushUpdatesHelper(leveldbPersistence, docName, updates)\n\n  const keys = await leveldbPersistence._transact(db => getLevelUpdatesEntries(db, docName))\n\n  for (let i = 0; i < keys.length; i++) {\n    t.assert(keys[i].key[3] === i)\n  }\n\n  const yarray = ydoc1.getArray('arr')\n  for (let i = 0; i < N; i++) {\n    yarray.insert(0, [i])\n  }\n  await flushUpdatesHelper(leveldbPersistence, docName, updates)\n\n  const ydoc2 = await leveldbPersistence.getYDoc(docName)\n  t.assert(ydoc2.getArray('arr').length === N)\n\n  await leveldbPersistence.flushDocument(docName)\n  const mergedKeys = await leveldbPersistence._transact(db => getLevelUpdatesEntries(db, docName))\n  t.assert(mergedKeys.length === 1)\n\n  // getYDoc still works after flush/merge\n  const ydoc3 = await leveldbPersistence.getYDoc(docName)\n  t.assert(ydoc3.getArray('arr').length === N)\n\n  // test if state vector is properly generated\n  t.compare(Y.encodeStateVector(ydoc1), await leveldbPersistence.getStateVector(docName))\n  // add new update so that sv needs to be updated\n  ydoc1.getArray('arr').insert(0, ['new'])\n  await flushUpdatesHelper(leveldbPersistence, docName, updates)\n  t.compare(Y.encodeStateVector(ydoc1), await leveldbPersistence.getStateVector(docName))\n\n  await leveldbPersistence.destroy()\n}\n\n/**\n * @param {t.TestCase} tc\n */\nexport const testDiff = async tc => {\n  const N = PREFERRED_TRIM_SIZE * 2 // primes are awesome - ensure that the document is at least flushed once\n  const docName = tc.testName\n  const ydoc1 = new Y.Doc()\n  ydoc1.clientID = 0 // so we can check the state vector\n  const leveldbPersistence = new LeveldbPersistence(storageName)\n  await leveldbPersistence.clearDocument(docName)\n\n  /**\n   * @type {Array<Uint8Array>}\n   */\n  const updates = []\n  ydoc1.on('update', update => {\n    updates.push(update)\n  })\n\n  const yarray = ydoc1.getArray('arr')\n  // create N changes\n  for (let i = 0; i < N; i++) {\n    yarray.insert(0, [i])\n  }\n  await flushUpdatesHelper(leveldbPersistence, docName, updates)\n\n  // create partially merged doc\n  const ydoc2 = await leveldbPersistence.getYDoc(docName)\n\n  // another N updates\n  for (let i = 0; i < N; i++) {\n    yarray.insert(0, [i])\n  }\n  await flushUpdatesHelper(leveldbPersistence, docName, updates)\n\n  // apply diff to doc\n  const diffUpdate = await leveldbPersistence.getDiff(docName, Y.encodeStateVector(ydoc2))\n  Y.applyUpdate(ydoc2, diffUpdate)\n\n  t.assert(ydoc2.getArray('arr').length === ydoc1.getArray('arr').length)\n  t.assert(ydoc2.getArray('arr').length === N * 2)\n\n  await leveldbPersistence.destroy()\n}\n\n/**\n * @param {t.TestCase} tc\n */\nexport const testMetas = async tc => {\n  const docName = tc.testName\n  const leveldbPersistence = new LeveldbPersistence(storageName)\n  await leveldbPersistence.clearDocument(docName)\n\n  await leveldbPersistence.setMeta(docName, 'a', 4)\n  await leveldbPersistence.setMeta(docName, 'a', 5)\n  await leveldbPersistence.setMeta(docName, 'b', 4)\n  const a = await leveldbPersistence.getMeta(docName, 'a')\n  const b = await leveldbPersistence.getMeta(docName, 'b')\n  t.assert(a === 5)\n  t.assert(b === 4)\n  const metas = await leveldbPersistence.getMetas(docName)\n  t.assert(metas.size === 2)\n  t.assert(metas.get('a') === 5)\n  t.assert(metas.get('b') === 4)\n  await leveldbPersistence.delMeta(docName, 'a')\n  const c = await leveldbPersistence.getMeta(docName, 'a')\n  t.assert(c === undefined)\n  await leveldbPersistence.clearDocument(docName)\n  const metasEmpty = await leveldbPersistence.getMetas(docName)\n  t.assert(metasEmpty.size === 0)\n\n  await leveldbPersistence.destroy()\n}\n\n/**\n * @param {t.TestCase} tc\n */\nexport const testDeleteEmptySv = async tc => {\n  const docName = tc.testName\n  const leveldbPersistence = new LeveldbPersistence(storageName)\n  await leveldbPersistence.clearAll()\n\n  const ydoc = new Y.Doc()\n  ydoc.clientID = 0\n  ydoc.getArray('arr').insert(0, [1])\n  const singleUpdate = Y.encodeStateAsUpdate(ydoc)\n\n  t.compareArrays([], await leveldbPersistence.getAllDocNames())\n  await leveldbPersistence.storeUpdate(docName, singleUpdate)\n  t.compareArrays([docName], await leveldbPersistence.getAllDocNames())\n  const docSvs = await leveldbPersistence.getAllDocStateVectors()\n  t.assert(docSvs.length === 1)\n  t.compare([{ name: docName, clock: 0, sv: Y.encodeStateVector(ydoc) }], docSvs)\n\n  await leveldbPersistence.clearDocument(docName)\n  t.compareArrays([], await leveldbPersistence.getAllDocNames())\n  await leveldbPersistence.destroy()\n}\n\n/**\n * @param {t.TestCase} tc\n */\nexport const testMisc = async tc => {\n  const docName = tc.testName\n  const leveldbPersistence = new LeveldbPersistence(storageName)\n  await leveldbPersistence.clearDocument(docName)\n\n  const sv = await leveldbPersistence.getStateVector('does not exist')\n  t.assert(sv.byteLength === 1)\n\n  await leveldbPersistence.destroy()\n}\n", "import * as leveldb from './y-leveldb.tests.js'\n\nimport { runTests } from 'lib0/testing.js'\nimport { isBrowser, isNode } from 'lib0/environment.js'\nimport * as log from 'lib0/logging.js'\n\nif (isBrowser) {\n  log.createVConsole(document.body)\n}\nrunTests({\n  leveldb\n}).then(success => {\n  /* istanbul ignore next */\n  if (isNode) {\n    process.exit(success ? 0 : 1)\n  }\n})\n"], "names": ["encoding", "binary", "<PERSON><PERSON><PERSON>", "decoding", "promise", "EntryStream", "KeyStream", "ValueStream", "Y", "readStateVector", "DefaultLevel", "buffer", "t", "<PERSON><PERSON><PERSON><PERSON>", "log", "runTests", "isNode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUO,MAAM,mBAAmB,GAAG,IAAG;AACtC;AACA,MAAM,eAAe,GAAG,EAAC;AACzB,MAAM,eAAe,GAAG,EAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa,GAAG;AACtB,EAAE,MAAM,EAAE,IAAI;AACd,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,MAAM,2BAA2B,IAAI,IAAI,IAAI;AAC/C,EAAE,MAAM,2BAA2B,IAAI,IAAI,IAAI;AAC/C,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,oBAAoB,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK;AACtD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/B,IAAIA,mBAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAIC,iBAAM,CAAC,KAAK,EAAC;AAC7D,GAAG;AACH,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,mBAAmB,GAAG,OAAO,IAAI;AAC9C,EAAE,MAAM,IAAI;AACZ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;AACjC,KAAK,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACvC,KAAK,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACxC,KAAK,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,EAAC;AAC3C,EAAE,OAAO,CAAC,GAAG,IAAI,EAAC;AAClB,EAAE,OAAO,IAAI;AACb,EAAC;AACD;AACO,MAAM,WAAW,GAAG;AAC3B,EAAE,MAAM,EAAE,IAAI;AACd,EAAE,IAAI,EAAE,QAAQ;AAChB;AACA,EAAE,MAAM,4CAA4C,GAAG,IAAI;AAC3D,IAAI,MAAM,OAAO,GAAGD,mBAAQ,CAAC,aAAa,GAAE;AAC5C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,MAAM,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,EAAC;AACtB,MAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;AACjC,QAAQA,mBAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,eAAe,EAAC;AACrD,QAAQA,mBAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,EAAC;AAC3C,OAAO,iCAAiC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;AACnE,QAAQA,mBAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,eAAe,EAAC;AACrD,QAAQ,oBAAoB,CAAC,OAAO,EAAE,CAAC,EAAC;AACxC,OAAO,MAAM;AACb,QAAQ,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAC/C,OAAO;AACP,KAAK;AACL,IAAI,OAAOE,eAAM,CAAC,IAAI,CAACF,mBAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,MAAM,iCAAiC,GAAG,IAAI;AAChD,IAAI,MAAM,OAAO,GAAGG,mBAAQ,CAAC,aAAa,CAAC,GAAG,EAAC;AAC/C,IAAI,MAAM,GAAG,GAAG,GAAE;AAClB,IAAI,OAAOA,mBAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACzC,MAAM,QAAQA,mBAAQ,CAAC,SAAS,CAAC,OAAO,CAAC;AACzC,QAAQ,KAAK,eAAe;AAC5B,UAAU,GAAG,CAAC,IAAI,CAACA,mBAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,EAAC;AACnD,UAAU,KAAK;AACf,QAAQ,KAAK,eAAe;AAC5B,UAAU,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAC;AAChD,UAAU,KAAK;AACf,OAAO;AACP,KAAK;AACL,IAAI,OAAO,GAAG;AACd,GAAG;AACH,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,KAAK;AACpC,EAAE,IAAI,IAAG;AACT,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,EAAC;AAC3B,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB;AACA,IAAI,uBAAuB,CAAC,GAAG,EAAE,QAAQ,EAAE;AAC3C,MAAM,MAAM;AACZ,KAAK,MAAM;AACX,MAAM,MAAM,GAAG;AACf,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,EAAED,eAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,mBAAmB,GAAG,CAAC,EAAE,EAAE,IAAI,KAAKE,kBAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AACrF;AACA;AACA;AACA,EAAE,MAAM,MAAM,GAAG,GAAE;AACnB,EAAE,IAAIC,2BAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,IAAI;AAC/C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAC;AACrB,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM;AACrB,IAAI,OAAO,CAAC,MAAM,EAAC;AACnB,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAC;AACxB,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gBAAgB,GAAG,CAAC,EAAE,EAAE,IAAI,KAAKD,kBAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAClF;AACA;AACA;AACA,EAAE,MAAM,MAAM,GAAG,GAAE;AACnB,EAAE,IAAIE,yBAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,IAAI;AAC7C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAC;AACrB,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM;AACrB,IAAI,OAAO,CAAC,MAAM,EAAC;AACnB,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAC;AACxB,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,kBAAkB,GAAG,CAAC,EAAE,EAAE,IAAI,KAAKF,kBAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AACpF;AACA;AACA;AACA,EAAE,MAAM,MAAM,GAAG,GAAE;AACnB,EAAE,IAAIG,2BAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,IAAI;AAC/C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAC;AACrB,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM;AACrB,IAAI,OAAO,CAAC,MAAM,EAAC;AACnB,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,EAAC;AACxB,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,eAAe,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,KAAK,kBAAkB,CAAC,EAAE,EAAE;AAClF,EAAE,GAAG,EAAE,uBAAuB,CAAC,OAAO,EAAE,CAAC,CAAC;AAC1C,EAAE,EAAE,EAAE,uBAAuB,CAAC,OAAO,EAAEN,iBAAM,CAAC,MAAM,CAAC;AACrD,EAAE,GAAG,IAAI;AACT,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,sBAAsB,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,KAAK,mBAAmB,CAAC,EAAE,EAAE;AAC1F,EAAE,GAAG,EAAE,uBAAuB,CAAC,OAAO,EAAE,CAAC,CAAC;AAC1C,EAAE,EAAE,EAAE,uBAAuB,CAAC,OAAO,EAAEA,iBAAM,CAAC,MAAM,CAAC;AACrD,EAAE,GAAG,IAAI;AACT,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,mBAAmB,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,KAAK,gBAAgB,CAAC,EAAE,EAAE;AACpF,EAAE,GAAG,EAAE,uBAAuB,CAAC,OAAO,EAAE,CAAC,CAAC;AAC1C,EAAE,EAAE,EAAE,uBAAuB,CAAC,OAAO,EAAEA,iBAAM,CAAC,MAAM,CAAC;AACrD,EAAE,GAAG,IAAI;AACT,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAc,GAAG,CAAC,EAAE,KAAK,gBAAgB,CAAC,EAAE,EAAE;AAC3D,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC;AACf,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,UAAU,GAAG,CAAC,EAAE,KAAK,mBAAmB,CAAC,EAAE,EAAE;AAC1D,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC;AACf,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,qBAAqB,GAAG,CAAC,EAAE,EAAE,OAAO,KAAK,mBAAmB,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI;AACpI,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,IAAI,OAAO,CAAC,CAAC;AACb,GAAG,MAAM;AACT,IAAI,8BAA8B,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,GAAG;AACH,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK;AAC1C;AACA,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzB,IAAI,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAC;AAC/B,GAAG,MAAM;AACT,IAAI,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAC;AACxD,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAC;AACvD,IAAI,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,EAAC;AACvB,GAAG;AACH,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,iBAAiB,GAAG,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,uBAAuB,CAAC,OAAO,EAAE,EAAE,CAAC,EAAC;AACvJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,uBAAuB,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAC;AACpF;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qBAAqB,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAC;AACpF;AACA;AACA;AACA;AACA;AACA,MAAM,wBAAwB,GAAG,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAC;AACrE;AACA;AACA;AACA;AACA;AACA,MAAM,4BAA4B,GAAG,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,OAAO,EAAC;AACpE;AACA;AACA;AACA;AACA,MAAM,sBAAsB,GAAG,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,EAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qBAAqB,GAAG,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY,GAAG,CAAC,OAAO,KAAK;AAClC,EAAE,MAAM,IAAI,GAAG,IAAIO,YAAC,CAAC,GAAG,GAAE;AAC1B,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;AACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,MAAMA,YAAC,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAC;AACrC,KAAK;AACL,GAAG,EAAC;AACJ,EAAE,OAAO,EAAE,MAAM,EAAEA,YAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,EAAE,EAAEA,YAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;AAC/E,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,gBAAgB,GAAG,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,KAAK;AAC3D,EAAE,MAAM,OAAO,GAAGR,mBAAQ,CAAC,aAAa,GAAE;AAC1C,EAAEA,mBAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,EAAC;AACvC,EAAEA,mBAAQ,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,EAAC;AAC1C,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,4BAA4B,CAAC,OAAO,CAAC,EAAEA,mBAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,EAAC;AAC3F,EAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,wBAAwB,GAAG,GAAG,IAAI;AACxC,EAAE,MAAM,OAAO,GAAGG,mBAAQ,CAAC,aAAa,CAAC,GAAG,EAAC;AAC7C,EAAE,MAAM,KAAK,GAAGA,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAC7C,EAAE,MAAM,EAAE,GAAGA,mBAAQ,CAAC,iBAAiB,CAAC,OAAO,EAAC;AAChD,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE;AACtB,EAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMM,iBAAe,GAAG,OAAO,EAAE,EAAE,OAAO,KAAK;AAC/C,EAAE,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,EAAE,EAAE,4BAA4B,CAAC,OAAO,CAAC,EAAC;AACvE,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE;AACnB;AACA,IAAI,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE;AAClC,GAAG;AACH,EAAE,OAAO,wBAAwB,CAAC,GAAG,CAAC;AACtC,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,aAAa,GAAG,OAAO,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,KAAK;AACzE,EAAE,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE,aAAa,EAAC;AAC7D,EAAE,MAAM,gBAAgB,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAC;AACzD,EAAE,MAAM,iBAAiB,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAC;AAChD,EAAE,OAAO,KAAK;AACd,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,GAAG,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,KAAK;AACnD,EAAE,MAAM,KAAK,GAAG,MAAM,qBAAqB,CAAC,EAAE,EAAE,OAAO,EAAC;AACxD,EAAE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AACpB;AACA,IAAI,MAAM,IAAI,GAAG,IAAID,YAAC,CAAC,GAAG,GAAE;AAC5B,IAAIA,YAAC,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAC;AAC/B,IAAI,MAAM,EAAE,GAAGA,YAAC,CAAC,iBAAiB,CAAC,IAAI,EAAC;AACxC,IAAI,MAAM,gBAAgB,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,EAAC;AAC9C,GAAG;AACH,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,uBAAuB,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,MAAM,EAAC;AACzE,EAAE,OAAO,KAAK,GAAG,CAAC;AAClB,EAAC;AACD;AACO,MAAM,kBAAkB,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,QAAQ,6BAA6B,EAAE,KAAK,GAAGE,WAAY,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACtG;AACA;AACA;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE,GAAG,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,EAAC;AACnF,IAAI,IAAI,CAAC,EAAE,GAAGN,kBAAO,CAAC,OAAO,GAAE;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI;AAC1B,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAE;AAC5B,MAAM,IAAI,CAAC,EAAE,GAAG,CAAC,YAAY;AAC7B,QAAQ,MAAM,OAAM;AACpB,QAAQ,IAAI,GAAG,uBAAuB,IAAI,EAAC;AAC3C,QAAQ,IAAI;AACZ,UAAU,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,EAAC;AAC3B,SAAS,CAAC,OAAO,GAAG,EAAE;AACtB;AACA,UAAU,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,GAAG,EAAC;AACjE,SAAS;AACT,QAAQ,OAAO,GAAG;AAClB,OAAO,IAAG;AACV,MAAM,OAAO,IAAI,CAAC,EAAE;AACpB,MAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,EAAE,EAAE,OAAO,EAAC;AACxD,MAAM,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,OAAO,EAAC;AAClD,MAAM,MAAM,aAAa,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAC;AAClD,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,EAAE,EAAE,OAAO,EAAC;AACxD,MAAM,MAAM,IAAI,GAAG,IAAII,YAAC,CAAC,GAAG,GAAE;AAC9B,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM;AAC1B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,UAAUA,YAAC,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAC;AACzC,SAAS;AACT,OAAO,EAAC;AACR,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,mBAAmB,EAAE;AAChD,QAAQ,MAAM,aAAa,CAAC,EAAE,EAAE,OAAO,EAAEA,YAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAEA,YAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAC;AAChG,OAAO;AACP,MAAM,OAAO,IAAI;AACjB,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,MAAMC,iBAAe,CAAC,EAAE,EAAE,OAAO,EAAC;AAC9D,MAAM,IAAI,QAAQ,GAAG,CAAC,EAAC;AACvB,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE;AACvB,QAAQ,QAAQ,GAAG,MAAM,qBAAqB,CAAC,EAAE,EAAE,OAAO,EAAC;AAC3D,OAAO;AACP,MAAM,IAAI,EAAE,KAAK,IAAI,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC7C,QAAQ,OAAO,EAAE;AACjB,OAAO,MAAM;AACb;AACA,QAAQ,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,EAAE,EAAE,OAAO,EAAC;AAC1D,QAAQ,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC,OAAO,EAAC;AACpD,QAAQ,MAAM,aAAa,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAC;AACpD,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACjE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,OAAO,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;AACvC,IAAI,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC;AAC5C,IAAI,OAAOD,YAAC,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAC;AACzD,MAAM,MAAM,UAAU,CAAC,EAAE,EAAE,sBAAsB,CAAC,OAAO,CAAC,EAAE,qBAAqB,CAAC,OAAO,CAAC,EAAC;AAC3F,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE;AACpC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,EAAE,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAEG,iBAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/G,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AAChF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,EAAE,EAAE,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,EAAC;AAC7E,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE;AACvB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,OAAOA,iBAAM,CAAC,SAAS,CAAC,GAAG,CAAC;AAClC,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,EAAE,EAAC;AAC9C,MAAM,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qBAAqB,CAAC,GAAG;AAC3B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,EAAE,EAAC;AACvC,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;AAC7B,QAAQ,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,wBAAwB,CAAC,GAAG,CAAC,KAAK,EAAC;AACjE,QAAQ,OAAO,EAAE,IAAI,yBAAyB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE;AACtE,OAAO,CAAC;AACR,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI;AACtC,MAAM,MAAM,IAAI,GAAG,MAAM,mBAAmB,CAAC,EAAE,EAAE;AACjD,QAAQ,GAAG,EAAE,qBAAqB,CAAC,OAAO,EAAE,EAAE,CAAC;AAC/C,QAAQ,EAAE,EAAE,wBAAwB,CAAC,OAAO,CAAC;AAC7C,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,MAAM,EAAE,IAAI;AACpB,OAAO,EAAC;AACR,MAAM,MAAM,KAAK,GAAG,IAAI,GAAG,GAAE;AAC7B,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEA,iBAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAC,EAAE,EAAC;AAC3E,MAAM,OAAO,KAAK;AAClB,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;AACjD,GAAG;AACH;;AC7nBA;AACA,MAAM,WAAW,GAAG,sBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,eAAe,GAAG,OAAO,IAAI;AACnC,EAAE,MAAM,EAAE,GAAG,IAAI,GAAG,GAAE;AACtB,EAAE,MAAM,QAAQ,GAAGR,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAChD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACrC,IAAI,MAAM,MAAM,GAAGA,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAChD,IAAI,MAAM,KAAK,GAAGA,mBAAQ,CAAC,WAAW,CAAC,OAAO,EAAC;AAC/C,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAC;AACzB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,iBAAiB,GAAG,YAAY,IAAI,eAAe,CAACA,mBAAQ,CAAC,aAAa,CAAC,YAAY,CAAC,EAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,kBAAkB,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO;AACjD,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAC;AAChF;AACA;AACA;AACA;AACO,MAAM,wBAAwB,GAAG,MAAM,EAAE,IAAI;AACpD,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC,SAAQ;AAC7B,EAAE,MAAM,KAAK,GAAG,IAAIK,YAAC,CAAC,GAAG,GAAE;AAC3B,EAAE,KAAK,CAAC,QAAQ,GAAG,EAAC;AACpB,EAAE,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,WAAW,EAAC;AAChE;AACA,EAAE,MAAM,kBAAkB,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,EAAC;AAC5D,EAAEI,YAAC,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,kBAAkB,CAAC,cAAc,EAAE,EAAC;AAChE;AACA;AACA;AACA;AACA,EAAE,MAAM,OAAO,GAAG,GAAE;AACpB;AACA,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,IAAI;AAC/B,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAC;AACxB,GAAG,EAAC;AACJ;AACA,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;AACtC,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;AACtC;AACA,EAAE,MAAM,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAC;AAChE;AACA,EAAE,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,cAAc,CAAC,OAAO,EAAC;AACpE,EAAE,MAAM,EAAE,GAAG,iBAAiB,CAAC,SAAS,EAAC;AACzC,EAAEA,YAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAC;AACzB,EAAEA,YAAC,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAC;AAC3B;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAC;AACzD,EAAEA,YAAC,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC;AAC1D;AACA,EAAE,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,mBAAmB,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC;AACtH,EAAEA,YAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,kBAAkB,EAAC;AAClD;AACA,EAAEA,YAAC,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,kBAAkB,CAAC,cAAc,EAAE,EAAC;AACvE,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD,EAAEA,YAAC,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,kBAAkB,CAAC,cAAc,EAAE,EAAC;AAChE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,mBAAmB,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC;AACvH,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAC;AACvB,EAAEA,YAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,yBAAyB,EAAC;AAC5D;AACA,EAAE,MAAM,kBAAkB,CAAC,OAAO,GAAE;AACpC,EAAC;AACD;AACA;AACA;AACA;AACO,MAAM,qBAAqB,GAAG,MAAM,EAAE,IAAI;AACjD,EAAE,MAAM,CAAC,GAAG,mBAAmB,GAAG,EAAC;AACnC,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC,SAAQ;AAC7B,EAAE,MAAM,KAAK,GAAG,IAAIJ,YAAC,CAAC,GAAG,GAAE;AAC3B,EAAE,KAAK,CAAC,QAAQ,GAAG,EAAC;AACpB,EAAE,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,WAAW,EAAC;AAChE,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD;AACA;AACA;AACA;AACA,EAAE,MAAM,OAAO,GAAG,GAAE;AACpB;AACA,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,IAAI;AAC/B,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAC;AACxB,GAAG,EAAC;AACJ,EAAE,MAAM,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAC;AAChE;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC,EAAE,IAAI,sBAAsB,CAAC,EAAE,EAAE,OAAO,CAAC,EAAC;AAC5F;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAII,YAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAC;AAClC,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAC;AACtC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;AACzB,GAAG;AACH,EAAE,MAAM,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAC;AAChE;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAC;AACzD,EAAEA,YAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAC;AAC9C;AACA,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD,EAAE,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC,EAAE,IAAI,sBAAsB,CAAC,EAAE,EAAE,OAAO,CAAC,EAAC;AAClG,EAAEA,YAAC,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAC;AACnC;AACA;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAC;AACzD,EAAEA,YAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAC;AAC9C;AACA;AACA,EAAEA,YAAC,CAAC,OAAO,CAACJ,YAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,MAAM,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAC;AACzF;AACA,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAC;AAC1C,EAAE,MAAM,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAC;AAChE,EAAEI,YAAC,CAAC,OAAO,CAACJ,YAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE,MAAM,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAC;AACzF;AACA,EAAE,MAAM,kBAAkB,CAAC,OAAO,GAAE;AACpC,EAAC;AACD;AACA;AACA;AACA;AACO,MAAM,QAAQ,GAAG,MAAM,EAAE,IAAI;AACpC,EAAE,MAAM,CAAC,GAAG,mBAAmB,GAAG,EAAC;AACnC,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC,SAAQ;AAC7B,EAAE,MAAM,KAAK,GAAG,IAAIA,YAAC,CAAC,GAAG,GAAE;AAC3B,EAAE,KAAK,CAAC,QAAQ,GAAG,EAAC;AACpB,EAAE,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,WAAW,EAAC;AAChE,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD;AACA;AACA;AACA;AACA,EAAE,MAAM,OAAO,GAAG,GAAE;AACpB,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,IAAI;AAC/B,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,EAAC;AACxB,GAAG,EAAC;AACJ;AACA,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAC;AACtC;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;AACzB,GAAG;AACH,EAAE,MAAM,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAC;AAChE;AACA;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAC;AACzD;AACA;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;AACzB,GAAG;AACH,EAAE,MAAM,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAC;AAChE;AACA;AACA,EAAE,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAEA,YAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAC;AAC1F,EAAEA,YAAC,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,EAAC;AAClC;AACA,EAAEI,YAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,EAAC;AACzE,EAAEA,YAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,EAAC;AAClD;AACA,EAAE,MAAM,kBAAkB,CAAC,OAAO,GAAE;AACpC,EAAC;AACD;AACA;AACA;AACA;AACO,MAAM,SAAS,GAAG,MAAM,EAAE,IAAI;AACrC,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC,SAAQ;AAC7B,EAAE,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,WAAW,EAAC;AAChE,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD;AACA,EAAE,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAC;AACnD,EAAE,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAC;AACnD,EAAE,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,EAAC;AACnD,EAAE,MAAM,CAAC,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAC;AAC1D,EAAE,MAAM,CAAC,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAC;AAC1D,EAAEA,YAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,EAAC;AACnB,EAAEA,YAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,EAAC;AACnB,EAAE,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAC;AAC1D,EAAEA,YAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,EAAC;AAC5B,EAAEA,YAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAC;AAChC,EAAEA,YAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAC;AAChC,EAAE,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAC;AAChD,EAAE,MAAM,CAAC,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAC;AAC1D,EAAEA,YAAC,CAAC,MAAM,CAAC,CAAC,KAAK,SAAS,EAAC;AAC3B,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD,EAAE,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAC;AAC/D,EAAEA,YAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAC;AACjC;AACA,EAAE,MAAM,kBAAkB,CAAC,OAAO,GAAE;AACpC,EAAC;AACD;AACA;AACA;AACA;AACO,MAAM,iBAAiB,GAAG,MAAM,EAAE,IAAI;AAC7C,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC,SAAQ;AAC7B,EAAE,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,WAAW,EAAC;AAChE,EAAE,MAAM,kBAAkB,CAAC,QAAQ,GAAE;AACrC;AACA,EAAE,MAAM,IAAI,GAAG,IAAIJ,YAAC,CAAC,GAAG,GAAE;AAC1B,EAAE,IAAI,CAAC,QAAQ,GAAG,EAAC;AACnB,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC;AACrC,EAAE,MAAM,YAAY,GAAGA,YAAC,CAAC,mBAAmB,CAAC,IAAI,EAAC;AAClD;AACA,EAAEI,YAAC,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,kBAAkB,CAAC,cAAc,EAAE,EAAC;AAChE,EAAE,MAAM,kBAAkB,CAAC,WAAW,CAAC,OAAO,EAAE,YAAY,EAAC;AAC7D,EAAEA,YAAC,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,kBAAkB,CAAC,cAAc,EAAE,EAAC;AACvE,EAAE,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,qBAAqB,GAAE;AACjE,EAAEA,YAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAC;AAC/B,EAAEA,YAAC,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAEJ,YAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,EAAC;AACjF;AACA,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD,EAAEI,YAAC,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,kBAAkB,CAAC,cAAc,EAAE,EAAC;AAChE,EAAE,MAAM,kBAAkB,CAAC,OAAO,GAAE;AACpC,EAAC;AACD;AACA;AACA;AACA;AACO,MAAM,QAAQ,GAAG,MAAM,EAAE,IAAI;AACpC,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC,SAAQ;AAC7B,EAAE,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,WAAW,EAAC;AAChE,EAAE,MAAM,kBAAkB,CAAC,aAAa,CAAC,OAAO,EAAC;AACjD;AACA,EAAE,MAAM,EAAE,GAAG,MAAM,kBAAkB,CAAC,cAAc,CAAC,gBAAgB,EAAC;AACtE,EAAEA,YAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,KAAK,CAAC,EAAC;AAC/B;AACA,EAAE,MAAM,kBAAkB,CAAC,OAAO,GAAE;AACpC;;;;;;;;;;;;AC7PA,IAAIC,wBAAS,EAAE;AACf,EAAEC,cAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAC;AACnC,CAAC;AACDC,UAAQ,CAAC;AACT,EAAE,OAAO;AACT,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI;AACnB;AACA,EAAE,IAAIC,qBAAM,EAAE;AACd,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAC;AACjC,GAAG;AACH,CAAC;;"}