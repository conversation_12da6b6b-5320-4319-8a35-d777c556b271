export function testLeveldbUpdateStorage(tc: t.TestCase): Promise<void>;
export function testEncodeManyUpdates(tc: t.TestCase): Promise<void>;
export function testDiff(tc: t.TestCase): Promise<void>;
export function testMetas(tc: t.TestCase): Promise<void>;
export function testDeleteEmptySv(tc: t.TestCase): Promise<void>;
export function testMisc(tc: t.TestCase): Promise<void>;
import * as t from 'lib0/testing.js';
//# sourceMappingURL=y-leveldb.tests.d.ts.map