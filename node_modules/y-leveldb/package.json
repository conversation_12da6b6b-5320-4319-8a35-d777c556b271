{"name": "y-leveldb", "version": "0.1.2", "description": "LevelDb database adapter for Yjs", "type": "module", "main": "./dist/y-leveldb.cjs", "module": "./src/y-leveldb.js", "types": "./dist/src/y-leveldb.d.ts", "sideEffects": false, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}, "scripts": {"clean": "rm -rf dist", "test": "rollup -c && nyc --check-coverage --lines 100 --branches 100 --functions 100 --statements 100 node ./dist/test.cjs", "dist": "rollup -c", "lint": "markdownlint README.md && standard && tsc", "preversion": "npm run lint && npm run test && npm run clean && npm run dist && tsc && test -e dist/src/y-leveldb.d.ts && test -e dist/y-leveldb.cjs", "debug": "concurrently 'rollup -wc' 'http-server -o .'"}, "files": ["dist/*", "src/*"], "standard": {"ignore": ["/dist", "/node_modules", "/docs"]}, "exports": {"./package.json": "./package.json", ".": {"import": "./src/y-leveldb.js", "require": "./dist/y-leveldb.cjs"}}, "repository": {"type": "git", "url": "git+https://github.com/yjs/y-leveldb.git"}, "keywords": ["<PERSON><PERSON><PERSON>", "CRDT", "offline", "shared editing", "collaboration", "concurrency"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/yjs/y-leveldb/issues"}, "homepage": "https://yjs.dev", "dependencies": {"level": "^6.0.1", "lib0": "^0.2.31"}, "devDependencies": {"@rollup/plugin-commonjs": "^13.0.0", "@rollup/plugin-node-resolve": "^8.1.0", "concurrently": "^3.6.1", "http-server": "^0.12.3", "jsdoc": "^3.6.3", "level-mem": "^5.0.1", "markdownlint-cli": "^0.23.2", "nyc": "^15.1.0", "rollup": "^2.20.0", "rollup-plugin-node-polyfills": "^0.2.1", "standard": "^14.3.4", "typescript": "^3.9.6", "y-protocols": "^1.0.0", "yjs": "^13.2.0"}, "peerDependencies": {"yjs": "^13.0.0"}}