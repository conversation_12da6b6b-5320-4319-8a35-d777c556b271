{"name": "encoding-down", "version": "6.3.0", "description": "LevelDOWN wrapper supporting levelup@1 encodings", "license": "MIT", "main": "index.js", "scripts": {"test": "standard && hallmark && nyc node test", "coverage": "nyc report --reporter=text-lcov | coveralls", "hallmark": "hallmark --fix", "dependency-check": "dependency-check . test/*.js", "prepublishOnly": "npm run dependency-check"}, "dependencies": {"abstract-leveldown": "^6.2.1", "inherits": "^2.0.3", "level-codec": "^9.0.0", "level-errors": "^2.0.0"}, "devDependencies": {"coveralls": "^3.0.2", "dependency-check": "^3.3.0", "hallmark": "^2.0.0", "level-community": "^3.0.0", "memdown": "^5.0.0", "nyc": "^14.0.0", "safe-buffer": "^5.1.1", "standard": "^14.0.0", "tape": "^4.8.0"}, "hallmark": {"community": "level-community"}, "repository": "Level/encoding-down", "homepage": "https://github.com/Level/encoding-down", "keywords": ["level"], "engines": {"node": ">=6"}}