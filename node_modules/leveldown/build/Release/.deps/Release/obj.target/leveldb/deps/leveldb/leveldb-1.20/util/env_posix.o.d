cmd_Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/env_posix.o := c++ -o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/env_posix.o ../deps/leveldb/leveldb-1.20/util/env_posix.cc '-DNODE_GYP_MODULE_NAME=leveldb' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DSNAPPY=1' '-DLEVELDB_PLATFORM_POSIX=1' '-DOS_MACOSX=1' -I/Users/<USER>/Library/Caches/node-gyp/23.6.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/23.6.0/src -I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/v8/include -I../deps/leveldb/leveldb-1.20 -I../deps/leveldb/leveldb-1.20/include -I../deps/snappy/mac -I../deps/snappy/snappy  -O3 -gdwarf-2 -fno-strict-aliasing -flto -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -Wno-sign-compare -Wno-unused-variable -Wno-unused-function -std=gnu++20 -stdlib=libc++ -fno-rtti -fno-exceptions -MMD -MF ./Release/.deps/Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/env_posix.o.d.raw   -c
Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/env_posix.o: \
  ../deps/leveldb/leveldb-1.20/util/env_posix.cc \
  ../deps/leveldb/leveldb-1.20/include/leveldb/env.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/status.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/slice.h \
  ../deps/leveldb/leveldb-1.20/port/port.h \
  ../deps/leveldb/leveldb-1.20/port/port_posix.h \
  ../deps/snappy/snappy/snappy.h \
  ../deps/snappy/mac/snappy-stubs-public.h \
  ../deps/leveldb/leveldb-1.20/port/atomic_pointer.h \
  ../deps/leveldb/leveldb-1.20/util/logging.h \
  ../deps/leveldb/leveldb-1.20/util/mutexlock.h \
  ../deps/leveldb/leveldb-1.20/port/thread_annotations.h \
  ../deps/leveldb/leveldb-1.20/util/posix_logger.h \
  ../deps/leveldb/leveldb-1.20/util/env_posix_test_helper.h
../deps/leveldb/leveldb-1.20/util/env_posix.cc:
../deps/leveldb/leveldb-1.20/include/leveldb/env.h:
../deps/leveldb/leveldb-1.20/include/leveldb/status.h:
../deps/leveldb/leveldb-1.20/include/leveldb/slice.h:
../deps/leveldb/leveldb-1.20/port/port.h:
../deps/leveldb/leveldb-1.20/port/port_posix.h:
../deps/snappy/snappy/snappy.h:
../deps/snappy/mac/snappy-stubs-public.h:
../deps/leveldb/leveldb-1.20/port/atomic_pointer.h:
../deps/leveldb/leveldb-1.20/util/logging.h:
../deps/leveldb/leveldb-1.20/util/mutexlock.h:
../deps/leveldb/leveldb-1.20/port/thread_annotations.h:
../deps/leveldb/leveldb-1.20/util/posix_logger.h:
../deps/leveldb/leveldb-1.20/util/env_posix_test_helper.h:
