cmd_Release/leveldb.a := rm -f Release/leveldb.a && ./gyp-mac-tool filter-libtool libtool  -static -o Release/leveldb.a Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/builder.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/db_impl.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/db_iter.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/filename.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/dbformat.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/log_reader.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/log_writer.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/memtable.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/repair.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/table_cache.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/version_edit.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/version_set.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/db/write_batch.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/helpers/memenv/memenv.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/port/port_posix_sse.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/block.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/block_builder.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/filter_block.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/format.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/iterator.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/merger.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/table.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/table_builder.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/table/two_level_iterator.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/arena.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/bloom.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/cache.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/coding.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/comparator.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/crc32c.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/env.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/filter_policy.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/hash.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/logging.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/options.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/status.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/port/port_posix.o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/env_posix.o
