# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := leveldb
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=leveldb' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DSNAPPY=1' \
	'-DLEVELDB_PLATFORM_POSIX=1' \
	'-DOS_MACOSX=1' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-O0 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter \
	-Wno-sign-compare \
	-Wno-unused-variable \
	-Wno-unused-function

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-std=gnu++20 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Debug :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Debug :=

INCS_Debug := \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/v8/include \
	-I$(srcdir)/deps/leveldb/leveldb-1.20 \
	-I$(srcdir)/deps/leveldb/leveldb-1.20/include \
	-I$(srcdir)/deps/snappy/mac \
	-I$(srcdir)/deps/snappy/snappy

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=leveldb' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DSNAPPY=1' \
	'-DLEVELDB_PLATFORM_POSIX=1' \
	'-DOS_MACOSX=1'

# Flags passed to all source files.
CFLAGS_Release := \
	-O3 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-flto \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter \
	-Wno-sign-compare \
	-Wno-unused-variable \
	-Wno-unused-function

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-std=gnu++20 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Release :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Release :=

INCS_Release := \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/23.6.0/deps/v8/include \
	-I$(srcdir)/deps/leveldb/leveldb-1.20 \
	-I$(srcdir)/deps/leveldb/leveldb-1.20/include \
	-I$(srcdir)/deps/snappy/mac \
	-I$(srcdir)/deps/snappy/snappy

OBJS := \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/builder.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/db_impl.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/db_iter.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/filename.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/dbformat.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/log_reader.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/log_writer.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/memtable.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/repair.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/table_cache.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/version_edit.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/version_set.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/db/write_batch.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/helpers/memenv/memenv.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/port/port_posix_sse.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/block.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/block_builder.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/filter_block.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/format.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/iterator.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/merger.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/table.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/table_builder.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/table/two_level_iterator.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/arena.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/bloom.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/cache.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/coding.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/comparator.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/crc32c.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/env.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/filter_policy.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/hash.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/logging.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/options.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/status.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/port/port_posix.o \
	$(obj).target/$(TARGET)/deps/leveldb/leveldb-1.20/util/env_posix.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))
$(OBJS): GYP_OBJCFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE)) $(CFLAGS_OBJC_$(BUILDTYPE))
$(OBJS): GYP_OBJCXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE)) $(CFLAGS_OBJCC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug :=

LDFLAGS_Release := \
	-mmacosx-version-min=11.0 \
	-arch \
	arm64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release :=

LIBS :=

$(builddir)/leveldb.a: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/leveldb.a: LIBS := $(LIBS)
$(builddir)/leveldb.a: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/leveldb.a: TOOLSET := $(TOOLSET)
$(builddir)/leveldb.a: $(OBJS) FORCE_DO_CMD
	$(call do_cmd,alink)

all_deps += $(builddir)/leveldb.a
# Add target alias
.PHONY: leveldb
leveldb: $(builddir)/leveldb.a

# Add target alias to "all" target.
.PHONY: all
all: leveldb

# Add target alias
.PHONY: leveldb
leveldb: $(builddir)/leveldb.a

# Short alias for building this static library.
.PHONY: leveldb.a
leveldb.a: $(builddir)/leveldb.a

# Add static library to "all" target.
.PHONY: all
all: $(builddir)/leveldb.a

