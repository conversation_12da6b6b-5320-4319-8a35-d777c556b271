# Changelog

_**If you are upgrading:** please see [`UPGRADING.md`](UPGRADING.md)._

## [Unreleased][unreleased]

## [2.0.1] - 2019-04-05

### Changed

- Upgrade `standard` devDependency from `^11.0.0` to `^12.0.0` ([#19](https://github.com/Level/errors/issues/19)) ([**@ralphtheninja**](https://github.com/ralphtheninja))
- Apply common project tweaks ([#20](https://github.com/Level/errors/issues/20), [#21](https://github.com/Level/errors/issues/21)) ([**@vweevers**](https://github.com/vweevers))
- Tweak license ([`b61e01b`](https://github.com/Level/errors/commit/b61e01b)) ([**@ralphtheninja**](https://github.com/ralphtheninja))
- Tweak copyright years for less maintenance ([`772e911`](https://github.com/Level/errors/commit/772e911)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Added

- Add `nyc` and `coveralls` ([#18](https://github.com/Level/errors/issues/18), [#22](https://github.com/Level/errors/issues/22)) ([**@ralphtheninja**](https://github.com/ralphtheninja), [**@vweevers**](https://github.com/vweevers))

### Removed

- Remove node 9 from travis ([`9254882`](https://github.com/Level/errors/commit/9254882)) ([**@ralphtheninja**](https://github.com/ralphtheninja))
- Remove experimental typings ([`0ad3b37`](https://github.com/Level/errors/commit/0ad3b37)) ([**@vweevers**](https://github.com/vweevers))
- Remove contributors from `package.json` ([`29e1ae1`](https://github.com/Level/errors/commit/29e1ae1)) ([**@ralphtheninja**](https://github.com/ralphtheninja))
- Remove copyright headers from code ([`f5e5e40`](https://github.com/Level/errors/commit/f5e5e40)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

## [2.0.0] - 2018-05-13

### Changed

- Tweak readme, add npm and node badges ([`f80d1b7`](https://github.com/Level/errors/commit/f80d1b7)) ([**@ralphtheninja**](https://github.com/ralphtheninja))
- Upgrade `standard` devDependency from `^10.0.3` to `^11.0.0` ([#12](https://github.com/Level/errors/issues/12)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Added

- Add node 10 ([`b002756`](https://github.com/Level/errors/commit/b002756)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Removed

- Drop node 0.12, 4, 5 and 7 ([`b002756`](https://github.com/Level/errors/commit/b002756), [`150a3c6`](https://github.com/Level/errors/commit/150a3c6)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

## [1.1.2] - 2017-11-13

### Changed

- Update README style ([#11](https://github.com/Level/errors/issues/11)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Added

- Add `standard` devDependency ([#10](https://github.com/Level/errors/issues/10)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

## [1.1.1] - 2017-09-10

### Fixed

- Fix error `cause` argument in typings ([#9](https://github.com/Level/errors/issues/9)) ([**@MeirionHughes**](https://github.com/MeirionHughes))

## [1.1.0] - 2017-09-10

### Changed

- Tweak README ([`64b4842`](https://github.com/Level/errors/commit/64b4842)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Added

- Add basic typings ([#8](https://github.com/Level/errors/issues/8)) ([**@MeirionHughes**](https://github.com/MeirionHughes))

## [1.0.5] - 2017-08-15

### Changed

- Update copyright year from 2015 to 2017 ([#7](https://github.com/Level/errors/issues/7)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Added

- Add node 4, 5, 6, 7 and 8 ([#7](https://github.com/Level/errors/issues/7)) ([**@ralphtheninja**](https://github.com/ralphtheninja))
- Add Greenkeeper ([#3](https://github.com/Level/errors/issues/3), [#6](https://github.com/Level/errors/issues/6)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Removed

- Drop node 0.10 and iojs ([#7](https://github.com/Level/errors/issues/7)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

## [1.0.4] - 2015-08-25

### Added

- Document API ([#2](https://github.com/Level/errors/issues/2)) ([**@juliangruber**](https://github.com/juliangruber))

## [1.0.3] - 2015-03-21

### Fixed

- Add node 0.10 to travis ([`fcc758c`](https://github.com/Level/errors/commit/fcc758c)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

## [1.0.2] - 2015-03-21

### Changed

- Tweak README to have same style as `codec` ([`d5e0cba`](https://github.com/Level/errors/commit/d5e0cba)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

### Added

- Add travis ([`5f6d1cd`](https://github.com/Level/errors/commit/5f6d1cd), [`58690fa`](https://github.com/Level/errors/commit/58690fa), [`36c3199`](https://github.com/Level/errors/commit/36c3199)) ([**@ralphtheninja**](https://github.com/ralphtheninja))

## 1.0.1 - 2015-03-20

:seedling: Initial release.

[unreleased]: https://github.com/Level/errors/compare/v2.0.1...HEAD

[2.0.1]: https://github.com/Level/errors/compare/v2.0.0...v2.0.1

[2.0.0]: https://github.com/Level/errors/compare/v1.1.2...v2.0.0

[1.1.2]: https://github.com/Level/errors/compare/v1.1.1...v1.1.2

[1.1.1]: https://github.com/Level/errors/compare/v1.1.0...v1.1.1

[1.1.0]: https://github.com/Level/errors/compare/v1.0.5...v1.1.0

[1.0.5]: https://github.com/Level/errors/compare/v1.0.4...v1.0.5

[1.0.4]: https://github.com/Level/errors/compare/v1.0.3...v1.0.4

[1.0.3]: https://github.com/Level/errors/compare/v1.0.2...v1.0.3

[1.0.2]: https://github.com/Level/errors/compare/v1.0.1...v1.0.2
