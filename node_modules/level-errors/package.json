{"name": "level-errors", "version": "2.0.1", "description": "Error types for levelup", "license": "MIT", "main": "errors.js", "scripts": {"test": "standard && hallmark && nyc node test.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "hallmark": "hallmark --fix", "dependency-check": "dependency-check . test.js", "prepublishOnly": "npm run dependency-check"}, "dependencies": {"errno": "~0.1.1"}, "devDependencies": {"coveralls": "^3.0.2", "dependency-check": "^3.3.0", "hallmark": "^0.1.0", "level-community": "^3.0.0", "nyc": "^13.2.0", "standard": "^12.0.0", "tape": "^4.3.0"}, "hallmark": {"community": "level-community"}, "repository": {"type": "git", "url": "https://github.com/Level/errors.git"}, "homepage": "https://github.com/Level/errors", "keywords": ["level", "leveldb", "levelup", "leveldown", "errors"], "engines": {"node": ">=6"}}