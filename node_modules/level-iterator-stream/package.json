{"name": "level-iterator-stream", "version": "4.0.2", "description": "Turn an abstract-leveldown iterator into a readable stream", "license": "MIT", "scripts": {"test": "standard && hallmark && (nyc -s node test.js | faucet) && nyc report", "coverage": "nyc report --reporter=text-lcov | coveralls", "hallmark": "hallmark --fix", "dependency-check": "dependency-check . test.js", "prepublishOnly": "npm run dependency-check"}, "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.4.0", "xtend": "^4.0.2"}, "devDependencies": {"coveralls": "^3.0.2", "dependency-check": "^3.3.0", "faucet": "^0.0.1", "hallmark": "^2.0.0", "level-community": "^3.0.0", "leveldown": "^5.0.0", "nyc": "^14.0.0", "secret-event-listener": "^1.0.0", "standard": "^14.0.0", "tape": "^4.4.0", "tempy": "0.2.1", "through2": "^3.0.0"}, "hallmark": {"community": "level-community"}, "repository": "Level/iterator-stream", "homepage": "https://github.com/Level/iterator-stream", "keywords": ["level"], "engines": {"node": ">=6"}, "greenkeeper": {"ignore": ["tempy"]}}