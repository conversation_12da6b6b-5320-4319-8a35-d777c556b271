const Y = require('yjs');
const { LeveldbPersistence } = require('y-leveldb');
const path = require('path');
const fs = require('fs');

/**
 * Persistence Service for YJS Documents
 * Follows Single Responsibility Principle - handles only document persistence
 * Follows Dependency Inversion Principle - depends on abstractions
 */
class PersistenceService {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    this.persistence = null;
    this.isEnabled = config.persistence;
    this.persistenceDir = config.persistenceDir || './yjs-data';
  }

  /**
   * Initialize persistence service
   */
  async initialize() {
    if (!this.isEnabled) {
      this.logger.info('Persistence is disabled');
      return;
    }

    try {
      // Ensure persistence directory exists
      if (!fs.existsSync(this.persistenceDir)) {
        fs.mkdirSync(this.persistenceDir, { recursive: true });
        this.logger.info('Created persistence directory', { dir: this.persistenceDir });
      }

      // Initialize LevelDB persistence
      this.persistence = new LeveldbPersistence(this.persistenceDir);

      this.logger.info('Persistence service initialized', {
        enabled: this.isEnabled,
        directory: this.persistenceDir
      });

    } catch (error) {
      this.logger.error('Failed to initialize persistence service', error);
      throw error;
    }
  }

  /**
   * Get persisted document state
   * @param {string} documentId - Document identifier
   * @returns {Promise<Uint8Array|null>} Document state or null if not found
   */
  async getDocumentState(documentId) {
    if (!this.isEnabled || !this.persistence) {
      return null;
    }

    try {
      const state = await this.persistence.getYDoc(documentId);
      if (state) {
        this.logger.debug('Retrieved document state from persistence', {
          documentId,
          stateSize: state.length
        });
        return state;
      }
      return null;
    } catch (error) {
      this.logger.error('Failed to get document state from persistence', error, {
        documentId
      });
      return null;
    }
  }

  /**
   * Store document state
   * @param {string} documentId - Document identifier
   * @param {Y.Doc} doc - YJS document
   */
  async storeDocumentState(documentId, doc) {
    if (!this.isEnabled || !this.persistence) {
      return;
    }

    try {
      await this.persistence.storeUpdate(documentId, Y.encodeStateAsUpdate(doc));
      
      this.logger.debug('Stored document state to persistence', {
        documentId,
        stateSize: Y.encodeStateAsUpdate(doc).length
      });

    } catch (error) {
      this.logger.error('Failed to store document state to persistence', error, {
        documentId
      });
    }
  }

  /**
   * Store document update
   * @param {string} documentId - Document identifier
   * @param {Uint8Array} update - YJS update
   */
  async storeUpdate(documentId, update) {
    if (!this.isEnabled || !this.persistence) {
      return;
    }

    try {
      await this.persistence.storeUpdate(documentId, update);
      
      this.logger.debug('Stored document update to persistence', {
        documentId,
        updateSize: update.length
      });

    } catch (error) {
      this.logger.error('Failed to store document update to persistence', error, {
        documentId,
        updateSize: update.length
      });
    }
  }

  /**
   * Load document from persistence and apply to YJS document
   * @param {string} documentId - Document identifier
   * @param {Y.Doc} doc - YJS document to load into
   */
  async loadDocument(documentId, doc) {
    if (!this.isEnabled || !this.persistence) {
      return false;
    }

    try {
      const persistedDoc = await this.persistence.getYDoc(documentId);
      if (persistedDoc) {
        const state = Y.encodeStateAsUpdate(persistedDoc);
        Y.applyUpdate(doc, state);
        
        this.logger.info('Loaded document from persistence', {
          documentId,
          stateSize: state.length
        });
        
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to load document from persistence', error, {
        documentId
      });
      return false;
    }
  }

  /**
   * Setup persistence for a document
   * @param {string} documentId - Document identifier
   * @param {Y.Doc} doc - YJS document
   */
  setupDocumentPersistence(documentId, doc) {
    if (!this.isEnabled || !this.persistence) {
      return;
    }

    // Store updates as they happen
    const updateHandler = (update, origin) => {
      // Don't persist updates that came from persistence loading
      if (origin !== 'persistence') {
        this.storeUpdate(documentId, update);
      }
    };

    doc.on('update', updateHandler);

    // Return cleanup function
    return () => {
      doc.off('update', updateHandler);
    };
  }

  /**
   * Get persistence statistics
   * @returns {Object} Persistence statistics
   */
  async getStats() {
    if (!this.isEnabled || !this.persistence) {
      return {
        enabled: false,
        directory: this.persistenceDir
      };
    }

    try {
      // Get directory size
      const getDirectorySize = (dirPath) => {
        let totalSize = 0;
        const files = fs.readdirSync(dirPath);
        
        for (const file of files) {
          const filePath = path.join(dirPath, file);
          const stats = fs.statSync(filePath);
          
          if (stats.isDirectory()) {
            totalSize += getDirectorySize(filePath);
          } else {
            totalSize += stats.size;
          }
        }
        
        return totalSize;
      };

      const directorySize = getDirectorySize(this.persistenceDir);

      return {
        enabled: true,
        directory: this.persistenceDir,
        directorySize: directorySize,
        directorySizeFormatted: this.formatBytes(directorySize)
      };

    } catch (error) {
      this.logger.error('Failed to get persistence stats', error);
      return {
        enabled: true,
        directory: this.persistenceDir,
        error: error.message
      };
    }
  }

  /**
   * Format bytes to human readable format
   * @param {number} bytes - Number of bytes
   * @returns {string} Formatted string
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Cleanup persistence resources
   */
  async destroy() {
    if (this.persistence) {
      try {
        await this.persistence.destroy();
        this.logger.info('Persistence service destroyed');
      } catch (error) {
        this.logger.error('Failed to destroy persistence service', error);
      }
    }
  }

  /**
   * Check if persistence is enabled
   * @returns {boolean} True if persistence is enabled
   */
  isEnabled() {
    return this.isEnabled;
  }
}

module.exports = PersistenceService;
