/**
 * Server Configuration Class
 * Follows Single Responsibility Principle - handles only configuration management
 */
class ServerConfig {
  constructor() {
    this.config = {
      port: process.env.PORT || 3000,
      host: process.env.HOST || '0.0.0.0',
      cors: {
        origin: process.env.CORS_ORIGIN || '*',
        methods: ['GET', 'POST'],
        credentials: true
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'combined'
      },
      yjs: {
        persistence: process.env.YJS_PERSISTENCE || false,
        persistenceDir: process.env.YJS_PERSISTENCE_DIR || './yjs-data',
        gcEnabled: process.env.YJS_GC_ENABLED !== 'false',
        cleanupInterval: parseInt(process.env.YJS_CLEANUP_INTERVAL) || 300000 // 5 minutes
      },
      websocket: {
        path: process.env.WS_PATH || '/yjs',
        maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS) || 1000,
        heartbeatInterval: parseInt(process.env.WS_HEARTBEAT_INTERVAL) || 30000
      }
    };
  }

  get(key) {
    return key ? this.config[key] : this.config;
  }

  set(key, value) {
    this.config[key] = value;
  }

  validate() {
    const requiredFields = ['port', 'host'];
    const missing = requiredFields.filter(field => !this.config[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required configuration fields: ${missing.join(', ')}`);
    }
    
    return true;
  }
}

module.exports = ServerConfig;
