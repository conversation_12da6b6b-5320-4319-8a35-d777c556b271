const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const cors = require('cors');
const helmet = require('helmet');

/**
 * Express Server Class with Y-WebSocket Integration
 * Follows Single Responsibility Principle - handles only HTTP server setup
 * Follows Open/Closed Principle - extensible for additional middleware
 */
class ExpressServer {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = null;
    this.isRunning = false;
    this.yWebSocketHandler = null;
  }

  /**
   * Initialize the Express server with middleware
   */
  initialize() {
    try {
      // Security middleware
      this.app.use(helmet({
        contentSecurityPolicy: false, // Allow WebSocket connections
        crossOriginEmbedderPolicy: false
      }));

      // CORS middleware
      this.app.use(cors(this.config.get('cors')));

      // Body parsing middleware
      this.app.use(express.json({ limit: '10mb' }));
      this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

      // Request logging middleware
      this.app.use((req, res, next) => {
        this.logger.http(`${req.method} ${req.url}`, {
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        next();
      });

      // Serve static files for examples and libraries
      this.app.use('/examples', express.static('examples'));
      this.app.use('/public', express.static('public'));

      // Health check endpoint
      this.app.get('/health', (req, res) => {
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime()
        });
      });

      // API routes
      this.setupApiRoutes();

      // Error handling middleware
      this.setupErrorHandling();

      this.logger.info('Express server initialized');
    } catch (error) {
      this.logger.error('Failed to initialize Express server', error);
      throw error;
    }
  }

  /**
   * Setup API routes
   */
  setupApiRoutes() {
    const apiRouter = express.Router();

    // Get server statistics
    apiRouter.get('/stats', async (req, res) => {
      try {
        if (this.yjsService) {
          const stats = await this.yjsService.getStats();
          res.json(stats);
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to get stats', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get document information
    apiRouter.get('/documents/:documentId', (req, res) => {
      try {
        const { documentId } = req.params;
        if (this.yjsService) {
          const info = this.yjsService.getDocumentInfo(documentId);
          res.json(info);
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to get document info', error, {
          documentId: req.params.documentId
        });
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Force document cleanup (admin endpoint)
    apiRouter.delete('/documents/:documentId', (req, res) => {
      try {
        const { documentId } = req.params;
        if (this.yjsService) {
          const removed = this.yjsService.cleanupDocument(documentId);
          res.json({ removed, documentId });
        } else {
          res.status(503).json({ error: 'YJS service not available' });
        }
      } catch (error) {
        this.logger.error('Failed to cleanup document', error, {
          documentId: req.params.documentId
        });
        res.status(400).json({ error: error.message });
      }
    });

    this.app.use('/api', apiRouter);
  }

  /**
   * Setup error handling middleware
   */
  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.url} not found`
      });
    });

    // Global error handler
    this.app.use((error, req, res, next) => {
      this.logger.error('Unhandled error in Express', error, {
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      });
    });
  }

  /**
   * Initialize WebSocket server for y-websocket
   * @param {YWebSocketHandler} yWebSocketHandler - Y-WebSocket handler instance
   */
  initializeWebSocketServer(yWebSocketHandler) {
    try {
      this.yWebSocketHandler = yWebSocketHandler;

      this.wss = new WebSocket.Server({
        server: this.server,
        path: '/yjs',
        // Handle CORS for WebSocket connections
        verifyClient: (info) => {
          const origin = info.origin;
          const corsOrigin = this.config.get('cors').origin;

          // Allow all origins if CORS is set to '*'
          if (corsOrigin === '*') {
            return true;
          }

          // Check if origin matches CORS configuration
          if (Array.isArray(corsOrigin)) {
            return corsOrigin.includes(origin);
          }

          return corsOrigin === origin;
        }
      });

      // Handle WebSocket connections
      this.wss.on('connection', (ws, req) => {
        this.yWebSocketHandler.handleConnection(ws, req);
      });

      // Handle WebSocket server errors
      this.wss.on('error', (error) => {
        this.logger.error('WebSocket server error', error);
      });

      this.logger.info('Y-WebSocket server initialized', {
        path: '/yjs',
        cors: this.config.get('cors').origin
      });

      return this.wss;
    } catch (error) {
      this.logger.error('Failed to initialize WebSocket server', error);
      throw error;
    }
  }

  /**
   * Set YJS service reference for API endpoints
   */
  setYjsService(yjsService) {
    this.yjsService = yjsService;
  }

  /**
   * Set Y-WebSocket handler reference
   */
  setYWebSocketHandler(yWebSocketHandler) {
    this.yWebSocketHandler = yWebSocketHandler;
  }

  /**
   * Start the server
   */
  async start() {
    try {
      const port = this.config.get('port');
      const host = this.config.get('host');

      await new Promise((resolve, reject) => {
        this.server.listen(port, host, (error) => {
          if (error) {
            reject(error);
          } else {
            this.isRunning = true;
            this.logger.info(`Server started on ${host}:${port}`);
            resolve();
          }
        });
      });
    } catch (error) {
      this.logger.error('Failed to start server', error);
      throw error;
    }
  }

  /**
   * Stop the server
   */
  async stop() {
    try {
      if (!this.isRunning) {
        return;
      }

      await new Promise((resolve) => {
        this.server.close(() => {
          this.isRunning = false;
          this.logger.info('Server stopped');
          resolve();
        });
      });
    } catch (error) {
      this.logger.error('Failed to stop server', error);
      throw error;
    }
  }

  /**
   * Get WebSocket server instance
   */
  getWebSocketServer() {
    return this.wss;
  }

  /**
   * Check if server is running
   */
  isServerRunning() {
    return this.isRunning;
  }
}

module.exports = ExpressServer;
