const IConnectionManager = require('../interfaces/IConnectionManager');

/**
 * Connection Manager Implementation
 * Follows Single Responsibility Principle - manages only WebSocket connections
 * Follows Dependency Inversion Principle - depends on abstractions (Logger)
 */
class ConnectionManager extends IConnectionManager {
  constructor(logger) {
    super();
    this.logger = logger;
    this.connections = new Map(); // connectionId -> connection object
    this.documentConnections = new Map(); // documentId -> Set of connectionIds
  }

  addConnection(connectionId, socket, metadata = {}) {
    try {
      const connection = {
        id: connectionId,
        socket: socket,
        documentId: metadata.documentId,
        userId: metadata.userId,
        joinedAt: new Date(),
        lastActivity: new Date(),
        ...metadata
      };

      this.connections.set(connectionId, connection);

      // Track document connections
      if (connection.documentId) {
        if (!this.documentConnections.has(connection.documentId)) {
          this.documentConnections.set(connection.documentId, new Set());
        }
        this.documentConnections.get(connection.documentId).add(connectionId);
      }

      this.logger.info('Connection added', {
        connectionId,
        documentId: connection.documentId,
        userId: connection.userId
      });

      return connection;
    } catch (error) {
      this.logger.error('Failed to add connection', error, { connectionId });
      throw error;
    }
  }

  removeConnection(connectionId) {
    try {
      const connection = this.connections.get(connectionId);
      if (!connection) {
        this.logger.warn('Attempted to remove non-existent connection', { connectionId });
        return false;
      }

      // Remove from document connections
      if (connection.documentId) {
        const docConnections = this.documentConnections.get(connection.documentId);
        if (docConnections) {
          docConnections.delete(connectionId);
          if (docConnections.size === 0) {
            this.documentConnections.delete(connection.documentId);
          }
        }
      }

      this.connections.delete(connectionId);

      this.logger.info('Connection removed', {
        connectionId,
        documentId: connection.documentId,
        duration: Date.now() - connection.joinedAt.getTime()
      });

      return true;
    } catch (error) {
      this.logger.error('Failed to remove connection', error, { connectionId });
      throw error;
    }
  }

  getConnection(connectionId) {
    return this.connections.get(connectionId) || null;
  }

  getConnectionsByDocument(documentId) {
    const connectionIds = this.documentConnections.get(documentId);
    if (!connectionIds) return [];

    return Array.from(connectionIds)
      .map(id => this.connections.get(id))
      .filter(conn => conn !== undefined);
  }

  getConnectionCount() {
    return this.connections.size;
  }

  broadcast(documentId, message, excludeConnectionId = null) {
    try {
      const connections = this.getConnectionsByDocument(documentId);
      let broadcastCount = 0;

      connections.forEach(connection => {
        if (connection.id !== excludeConnectionId && connection.socket) {
          try {
            // For WebSocket connections, send as JSON string
            if (connection.socket.readyState === 1) { // WebSocket.OPEN
              connection.socket.send(JSON.stringify(message));
              broadcastCount++;
            }
          } catch (error) {
            this.logger.error('Failed to send message to connection', error, {
              connectionId: connection.id,
              documentId
            });
          }
        }
      });

      this.logger.debug('Message broadcasted', {
        documentId,
        broadcastCount,
        excludeConnectionId
      });

      return broadcastCount;
    } catch (error) {
      this.logger.error('Failed to broadcast message', error, { documentId });
      throw error;
    }
  }

  updateLastActivity(connectionId) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastActivity = new Date();
    }
  }

  getConnectionStats() {
    const stats = {
      totalConnections: this.connections.size,
      documentsWithConnections: this.documentConnections.size,
      connectionsByDocument: {}
    };

    this.documentConnections.forEach((connections, documentId) => {
      stats.connectionsByDocument[documentId] = connections.size;
    });

    return stats;
  }
}

module.exports = ConnectionManager;
