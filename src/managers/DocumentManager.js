const Y = require('yjs');
const IDocumentManager = require('../interfaces/IDocumentManager');

/**
 * Document Manager Implementation
 * Follows Single Responsibility Principle - manages only YJS documents
 * Follows Open/Closed Principle - extensible for different persistence strategies
 */
class DocumentManager extends IDocumentManager {
  constructor(logger, config = {}, persistenceService = null) {
    super();
    this.logger = logger;
    this.config = config;
    this.persistenceService = persistenceService;
    this.documents = new Map(); // documentId -> YJS document
    this.documentStats = new Map(); // documentId -> stats object
    this.persistenceCleanupHandlers = new Map(); // documentId -> cleanup function
    this.gcEnabled = config.gcEnabled !== false;

    // Setup cleanup interval
    if (config.cleanupInterval) {
      this.cleanupInterval = setInterval(() => {
        this.cleanup();
      }, config.cleanupInterval);
    }
  }

  async getDocument(documentId) {
    try {
      if (!this.documents.has(documentId)) {
        const doc = new Y.Doc();

        // Enable garbage collection if configured
        if (this.gcEnabled) {
          doc.gc = true;
        }

        // Load from persistence if available
        if (this.persistenceService) {
          await this.persistenceService.loadDocument(documentId, doc);

          // Setup persistence for future updates
          const cleanupHandler = this.persistenceService.setupDocumentPersistence(documentId, doc);
          if (cleanupHandler) {
            this.persistenceCleanupHandlers.set(documentId, cleanupHandler);
          }
        }

        // Setup document event listeners
        this.setupDocumentListeners(doc, documentId);

        this.documents.set(documentId, doc);
        this.documentStats.set(documentId, {
          createdAt: new Date(),
          lastAccessed: new Date(),
          updateCount: 0,
          connectionCount: 0,
          persisted: this.persistenceService ? true : false
        });

        this.logger.info('Document created', {
          documentId,
          persisted: this.persistenceService ? true : false
        });
      }

      // Update last accessed time
      const stats = this.documentStats.get(documentId);
      if (stats) {
        stats.lastAccessed = new Date();
      }

      return this.documents.get(documentId);
    } catch (error) {
      this.logger.error('Failed to get document', error, { documentId });
      throw error;
    }
  }

  hasDocument(documentId) {
    return this.documents.has(documentId);
  }

  removeDocument(documentId) {
    try {
      const doc = this.documents.get(documentId);
      if (doc) {
        // Clean up persistence handler
        const cleanupHandler = this.persistenceCleanupHandlers.get(documentId);
        if (cleanupHandler) {
          cleanupHandler();
          this.persistenceCleanupHandlers.delete(documentId);
        }

        // Clean up document resources
        doc.destroy();
        this.documents.delete(documentId);
        this.documentStats.delete(documentId);

        this.logger.info('Document removed', { documentId });
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to remove document', error, { documentId });
      throw error;
    }
  }

  getDocumentStats(documentId) {
    const stats = this.documentStats.get(documentId);
    if (!stats) return null;

    const doc = this.documents.get(documentId);
    return {
      ...stats,
      size: doc ? doc.store.clients.size : 0,
      exists: this.hasDocument(documentId)
    };
  }

  getAllDocumentIds() {
    return Array.from(this.documents.keys());
  }

  cleanup() {
    try {
      const now = new Date();
      const maxIdleTime = this.config.maxIdleTime || 30 * 60 * 1000; // 30 minutes default
      let cleanedCount = 0;

      this.documentStats.forEach((stats, documentId) => {
        const idleTime = now - stats.lastAccessed;
        if (idleTime > maxIdleTime && stats.connectionCount === 0) {
          this.removeDocument(documentId);
          cleanedCount++;
        }
      });

      if (cleanedCount > 0) {
        this.logger.info('Document cleanup completed', { cleanedCount });
      }

      return cleanedCount;
    } catch (error) {
      this.logger.error('Document cleanup failed', error);
      throw error;
    }
  }

  applyUpdate(documentId, update, origin = null) {
    try {
      const doc = this.getDocument(documentId);
      Y.applyUpdate(doc, update, origin);

      // Update statistics
      const stats = this.documentStats.get(documentId);
      if (stats) {
        stats.updateCount++;
        stats.lastAccessed = new Date();
      }

      this.logger.debug('Update applied to document', {
        documentId,
        updateSize: update.length,
        origin
      });

      return true;
    } catch (error) {
      this.logger.error('Failed to apply update to document', error, {
        documentId,
        updateSize: update ? update.length : 0
      });
      throw error;
    }
  }

  setupDocumentListeners(doc, documentId) {
    // Listen for document updates
    doc.on('update', (update, origin) => {
      this.logger.debug('Document updated', {
        documentId,
        updateSize: update.length,
        origin
      });
    });

    // Listen for document destruction
    doc.on('destroy', () => {
      this.logger.debug('Document destroyed', { documentId });
    });
  }

  updateConnectionCount(documentId, count) {
    const stats = this.documentStats.get(documentId);
    if (stats) {
      stats.connectionCount = count;
    }
  }

  getOverallStats() {
    return {
      totalDocuments: this.documents.size,
      documentsWithStats: this.documentStats.size,
      gcEnabled: this.gcEnabled
    };
  }

  destroy() {
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Destroy all documents
    this.documents.forEach((doc, documentId) => {
      doc.destroy();
    });

    this.documents.clear();
    this.documentStats.clear();

    this.logger.info('DocumentManager destroyed');
  }
}

module.exports = DocumentManager;
