const Y = require('yjs');
const { encoding, decoding } = require('lib0');
const syncProtocol = require('y-protocols/sync');
const awarenessProtocol = require('y-protocols/awareness');

// Message types from y-websocket protocol
const messageSync = 0;
const messageAwareness = 1;

/**
 * Y-WebSocket Handler for YJS Integration
 * Follows Single Responsibility Principle - handles WebSocket connections for YJS
 * Follows Dependency Inversion Principle - depends on abstractions
 * Implements the standard y-websocket protocol
 */
class YWebSocketHandler {
  constructor(connectionManager, documentManager, logger) {
    this.connectionManager = connectionManager;
    this.documentManager = documentManager;
    this.logger = logger;
    this.connections = new Map(); // ws -> connection metadata
    this.awarenessMap = new Map(); // documentId -> awareness instance
  }

  /**
   * Handle new WebSocket connection using y-websocket protocol
   * @param {WebSocket} ws - WebSocket connection
   * @param {http.IncomingMessage} req - HTTP request
   */
  async handleConnection(ws, req) {
    const url = new URL(req.url, `http://${req.headers.host}`);
    const roomname = url.searchParams.get('room') || 'default';
    const userId = url.searchParams.get('userId') || `user-${Date.now()}`;

    this.logger.info('New WebSocket connection', {
      roomname,
      userId,
      ip: req.socket.remoteAddress
    });

    // Create connection metadata
    const connectionId = this.generateConnectionId();
    const connectionData = {
      id: connectionId,
      ws: ws,
      documentId: roomname,
      userId: userId,
      joinedAt: new Date(),
      lastActivity: new Date(),
      ip: req.socket.remoteAddress,
      synced: false
    };

    // Store connection
    this.connections.set(ws, connectionData);
    this.connectionManager.addConnection(connectionId, ws, connectionData);

    // Get or create document
    const doc = await this.documentManager.getDocument(roomname);

    // Get or create awareness for this document
    let awareness = this.awarenessMap.get(roomname);
    if (!awareness) {
      awareness = new awarenessProtocol.Awareness(doc);
      this.awarenessMap.set(roomname, awareness);
    }

    // Setup connection
    this.setupConnection(ws, doc, awareness, connectionData);

    // Handle messages
    ws.on('message', (message) => {
      this.handleMessage(ws, message, doc, awareness, connectionData);
    });

    // Handle connection close
    ws.on('close', () => {
      this.handleDisconnection(ws, connectionData);
    });

    // Handle connection errors
    ws.on('error', (error) => {
      this.handleError(ws, error, connectionData);
    });

    // Send initial sync
    this.sendSyncStep1(ws, doc);
  }

  /**
   * Setup connection with document and awareness
   * @param {WebSocket} ws - WebSocket connection
   * @param {Y.Doc} doc - YJS document
   * @param {awarenessProtocol.Awareness} awareness - Awareness instance
   * @param {Object} connectionData - Connection metadata
   */
  setupConnection(ws, doc, awareness, connectionData) {
    try {
      // Update connection count
      const connections = this.connectionManager.getConnectionsByDocument(connectionData.documentId);
      this.documentManager.updateConnectionCount(connectionData.documentId, connections.length);

      // Setup document update listener
      const updateHandler = (update, origin) => {
        if (origin !== connectionData.id) {
          this.broadcastUpdate(connectionData.documentId, update, connectionData.id);
        }
        this.handleDocumentUpdate(connectionData.documentId, update, origin);
      };

      doc.on('update', updateHandler);
      connectionData.updateHandler = updateHandler;

      // Setup awareness update listener
      const awarenessUpdateHandler = ({ added, updated, removed }) => {
        const changedClients = added.concat(updated, removed);
        this.broadcastAwareness(connectionData.documentId, awareness, changedClients, connectionData.id);
      };

      awareness.on('update', awarenessUpdateHandler);
      connectionData.awarenessUpdateHandler = awarenessUpdateHandler;

      this.logger.info('WebSocket connection established', {
        documentId: connectionData.documentId,
        userId: connectionData.userId,
        totalConnections: connections.length
      });

    } catch (error) {
      this.logger.error('Failed to setup connection', error, {
        documentId: connectionData.documentId,
        userId: connectionData.userId
      });
      ws.close(1011, 'Failed to setup connection');
    }
  }

  /**
   * Handle incoming WebSocket messages
   * @param {WebSocket} ws - WebSocket connection
   * @param {Buffer} message - Raw message
   * @param {Y.Doc} doc - YJS document
   * @param {awarenessProtocol.Awareness} awareness - Awareness instance
   * @param {Object} connectionData - Connection metadata
   */
  handleMessage(ws, message, doc, awareness, connectionData) {
    try {
      const decoder = decoding.createDecoder(message);
      const messageType = decoding.readVarUint(decoder);

      switch (messageType) {
        case messageSync:
          this.handleSyncMessage(ws, decoder, doc, connectionData);
          break;
        case messageAwareness:
          this.handleAwarenessMessage(decoder, awareness, connectionData);
          break;
        default:
          this.logger.warn('Unknown message type', { messageType, connectionId: connectionData.id });
      }

      // Update last activity
      connectionData.lastActivity = new Date();

    } catch (error) {
      this.logger.error('Failed to handle message', error, {
        connectionId: connectionData.id,
        documentId: connectionData.documentId
      });
    }
  }

  /**
   * Handle sync protocol messages
   * @param {WebSocket} ws - WebSocket connection
   * @param {decoding.Decoder} decoder - Message decoder
   * @param {Y.Doc} doc - YJS document
   * @param {Object} connectionData - Connection metadata
   */
  handleSyncMessage(ws, decoder, doc, connectionData) {
    const encoder = encoding.createEncoder();
    encoding.writeVarUint(encoder, messageSync);

    const syncMessageType = syncProtocol.readSyncMessage(decoder, encoder, doc, connectionData.id);

    if (syncMessageType === syncProtocol.messageYjsSyncStep2 && !connectionData.synced) {
      connectionData.synced = true;
      this.logger.debug('Client synced', {
        connectionId: connectionData.id,
        documentId: connectionData.documentId
      });
    }

    // Send response if encoder has content
    const response = encoding.toUint8Array(encoder);
    if (response.length > 1) {
      this.sendMessage(ws, response);
    }
  }

  /**
   * Handle awareness protocol messages
   * @param {decoding.Decoder} decoder - Message decoder
   * @param {awarenessProtocol.Awareness} awareness - Awareness instance
   * @param {Object} connectionData - Connection metadata
   */
  handleAwarenessMessage(decoder, awareness, connectionData) {
    awarenessProtocol.applyAwarenessUpdate(awareness, decoding.readVarUint8Array(decoder), connectionData.id);
  }

  /**
   * Send initial sync step 1
   * @param {WebSocket} ws - WebSocket connection
   * @param {Y.Doc} doc - YJS document
   */
  sendSyncStep1(ws, doc) {
    const encoder = encoding.createEncoder();
    encoding.writeVarUint(encoder, messageSync);
    syncProtocol.writeSyncStep1(encoder, doc);
    this.sendMessage(ws, encoding.toUint8Array(encoder));
  }

  /**
   * Send message to WebSocket
   * @param {WebSocket} ws - WebSocket connection
   * @param {Uint8Array} message - Message to send
   */
  sendMessage(ws, message) {
    if (ws.readyState === ws.OPEN) {
      ws.send(message);
    }
  }

  /**
   * Broadcast update to all connections in document except sender
   * @param {string} documentId - Document identifier
   * @param {Uint8Array} update - YJS update
   * @param {string} excludeConnectionId - Connection to exclude
   */
  broadcastUpdate(documentId, update, excludeConnectionId) {
    const encoder = encoding.createEncoder();
    encoding.writeVarUint(encoder, messageSync);
    syncProtocol.writeUpdate(encoder, update);
    const message = encoding.toUint8Array(encoder);

    this.broadcastMessage(documentId, message, excludeConnectionId);
  }

  /**
   * Broadcast awareness update
   * @param {string} documentId - Document identifier
   * @param {awarenessProtocol.Awareness} awareness - Awareness instance
   * @param {Array} changedClients - Changed client IDs
   * @param {string} excludeConnectionId - Connection to exclude
   */
  broadcastAwareness(documentId, awareness, changedClients, excludeConnectionId) {
    const encoder = encoding.createEncoder();
    encoding.writeVarUint(encoder, messageAwareness);
    encoding.writeVarUint8Array(encoder, awarenessProtocol.encodeAwarenessUpdate(awareness, changedClients));
    const message = encoding.toUint8Array(encoder);

    this.broadcastMessage(documentId, message, excludeConnectionId);
  }

  /**
   * Broadcast message to all connections in document
   * @param {string} documentId - Document identifier
   * @param {Uint8Array} message - Message to broadcast
   * @param {string} excludeConnectionId - Connection to exclude
   */
  broadcastMessage(documentId, message, excludeConnectionId) {
    const connections = this.connectionManager.getConnectionsByDocument(documentId);
    let broadcastCount = 0;

    connections.forEach(connection => {
      if (connection.id !== excludeConnectionId && connection.socket) {
        this.sendMessage(connection.socket, message);
        broadcastCount++;
      }
    });

    this.logger.debug('Message broadcasted', {
      documentId,
      broadcastCount,
      excludeConnectionId
    });
  }

  /**
   * Handle document updates (called by YJS document event)
   * @param {string} documentId - Document identifier
   * @param {Uint8Array} update - YJS update
   * @param {any} origin - Update origin
   */
  handleDocumentUpdate(documentId, update, origin) {
    try {
      // Update statistics
      const stats = this.documentManager.documentStats.get(documentId);
      if (stats) {
        stats.updateCount++;
        stats.lastAccessed = new Date();
      }

      this.logger.debug('Document update processed', {
        documentId,
        updateSize: update.length,
        origin: origin ? 'client' : 'server'
      });

    } catch (error) {
      this.logger.error('Failed to handle document update', error, {
        documentId,
        updateSize: update ? update.length : 0
      });
    }
  }

  /**
   * Generate unique connection ID
   * @returns {string} Unique connection identifier
   */
  generateConnectionId() {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle WebSocket disconnection
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} connectionData - Connection metadata
   */
  handleDisconnection(ws, connectionData) {
    try {
      const { id: connectionId, documentId, userId } = connectionData;

      // Clean up document event listener
      if (connectionData.updateHandler) {
        const doc = this.documentManager.getDocument(documentId);
        doc.off('update', connectionData.updateHandler);
      }

      // Clean up awareness event listener
      if (connectionData.awarenessUpdateHandler) {
        const awareness = this.awarenessMap.get(documentId);
        if (awareness) {
          awareness.off('update', connectionData.awarenessUpdateHandler);
          // Remove client from awareness
          awareness.setLocalState(null);
        }
      }

      // Remove connection
      this.connections.delete(ws);
      this.connectionManager.removeConnection(connectionId);

      // Update document connection count
      if (documentId) {
        const remainingConnections = this.connectionManager.getConnectionsByDocument(documentId);
        this.documentManager.updateConnectionCount(documentId, remainingConnections.length);

        // Clean up awareness if no more connections
        if (remainingConnections.length === 0) {
          const awareness = this.awarenessMap.get(documentId);
          if (awareness) {
            awareness.destroy();
            this.awarenessMap.delete(documentId);
          }
        }
      }

      this.logger.info('WebSocket disconnected', {
        connectionId,
        documentId,
        userId,
        remainingConnections: documentId ?
          this.connectionManager.getConnectionsByDocument(documentId).length : 0
      });

    } catch (error) {
      this.logger.error('Failed to handle disconnection', error, {
        connectionId: connectionData.id
      });
    }
  }

  /**
   * Handle WebSocket errors
   * @param {WebSocket} ws - WebSocket connection
   * @param {Error} error - Error object
   * @param {Object} connectionData - Connection metadata
   */
  handleError(ws, error, connectionData) {
    this.logger.error('WebSocket error occurred', error, {
      connectionId: connectionData.id,
      documentId: connectionData.documentId,
      userId: connectionData.userId
    });
  }

  /**
   * Get connection statistics
   * @returns {Object} Connection statistics
   */
  getConnectionStats() {
    return {
      totalConnections: this.connections.size,
      connectionsByDocument: this.getConnectionsByDocument()
    };
  }

  /**
   * Get connections grouped by document
   * @returns {Object} Connections grouped by document ID
   */
  getConnectionsByDocument() {
    const result = {};
    for (const [ws, connectionData] of this.connections) {
      const { documentId } = connectionData;
      if (!result[documentId]) {
        result[documentId] = 0;
      }
      result[documentId]++;
    }
    return result;
  }
}

module.exports = YWebSocketHandler;
