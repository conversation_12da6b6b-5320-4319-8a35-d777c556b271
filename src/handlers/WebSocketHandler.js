const Y = require('yjs');
const { setupWSConnection } = require('y-websocket/bin/utils');

/**
 * Y-WebSocket Handler for YJS Integration
 * Follows Single Responsibility Principle - handles WebSocket connections for YJS
 * Follows Dependency Inversion Principle - depends on abstractions
 * Integrates with y-websocket for standard YJS protocol support
 */
class YWebSocketHandler {
  constructor(connectionManager, documentManager, logger) {
    this.connectionManager = connectionManager;
    this.documentManager = documentManager;
    this.logger = logger;
    this.connections = new Map(); // ws -> connection metadata
  }

  /**
   * Handle new WebSocket connection using y-websocket
   * @param {WebSocket} ws - WebSocket connection
   * @param {http.IncomingMessage} req - HTTP request
   */
  handleConnection(ws, req) {
    const url = new URL(req.url, `http://${req.headers.host}`);
    const roomname = url.searchParams.get('room') || 'default';
    const userId = url.searchParams.get('userId') || `user-${Date.now()}`;

    this.logger.info('New WebSocket connection', {
      roomname,
      userId,
      ip: req.socket.remoteAddress
    });

    // Create connection metadata
    const connectionId = this.generateConnectionId();
    const connectionData = {
      id: connectionId,
      ws: ws,
      documentId: roomname,
      userId: userId,
      joinedAt: new Date(),
      lastActivity: new Date(),
      ip: req.socket.remoteAddress
    };

    // Store connection
    this.connections.set(ws, connectionData);
    this.connectionManager.addConnection(connectionId, ws, connectionData);

    // Get or create document
    const doc = this.documentManager.getDocument(roomname);

    // Setup y-websocket connection with custom document handling
    this.setupYWebSocketConnection(ws, req, doc, connectionData);

    // Handle connection close
    ws.on('close', () => {
      this.handleDisconnection(ws, connectionData);
    });

    // Handle connection errors
    ws.on('error', (error) => {
      this.handleError(ws, error, connectionData);
    });
  }

  /**
   * Setup y-websocket connection with custom document handling
   * @param {WebSocket} ws - WebSocket connection
   * @param {http.IncomingMessage} req - HTTP request
   * @param {Y.Doc} doc - YJS document
   * @param {Object} connectionData - Connection metadata
   */
  setupYWebSocketConnection(ws, req, doc, connectionData) {
    try {
      // Use y-websocket's setupWSConnection with our managed document
      const conn = setupWSConnection(ws, req, {
        docName: connectionData.documentId,
        gc: this.documentManager.config.gcEnabled !== false
      });

      // Override the document with our managed one
      if (conn && conn.doc) {
        // Transfer any existing content from the managed document
        const existingState = Y.encodeStateAsUpdate(doc);
        if (existingState.length > 0) {
          Y.applyUpdate(conn.doc, existingState);
        }

        // Replace the connection's document with our managed one
        conn.doc = doc;
      }

      // Update connection count
      const connections = this.connectionManager.getConnectionsByDocument(connectionData.documentId);
      this.documentManager.updateConnectionCount(connectionData.documentId, connections.length);

      // Setup document update listener for statistics
      const updateHandler = (update, origin) => {
        this.handleDocumentUpdate(connectionData.documentId, update, origin);
      };

      doc.on('update', updateHandler);

      // Store the handler for cleanup
      connectionData.updateHandler = updateHandler;

      this.logger.info('Y-WebSocket connection established', {
        documentId: connectionData.documentId,
        userId: connectionData.userId,
        totalConnections: connections.length
      });

    } catch (error) {
      this.logger.error('Failed to setup y-websocket connection', error, {
        documentId: connectionData.documentId,
        userId: connectionData.userId
      });
      ws.close(1011, 'Failed to setup connection');
    }
  }

  /**
   * Handle document updates (called by YJS document event)
   * @param {string} documentId - Document identifier
   * @param {Uint8Array} update - YJS update
   * @param {any} origin - Update origin
   */
  handleDocumentUpdate(documentId, update, origin) {
    try {
      // Update statistics
      const stats = this.documentManager.documentStats.get(documentId);
      if (stats) {
        stats.updateCount++;
        stats.lastAccessed = new Date();
      }

      this.logger.debug('Document update processed', {
        documentId,
        updateSize: update.length,
        origin: origin ? 'client' : 'server'
      });

    } catch (error) {
      this.logger.error('Failed to handle document update', error, {
        documentId,
        updateSize: update ? update.length : 0
      });
    }
  }

  /**
   * Generate unique connection ID
   * @returns {string} Unique connection identifier
   */
  generateConnectionId() {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle WebSocket disconnection
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} connectionData - Connection metadata
   */
  handleDisconnection(ws, connectionData) {
    try {
      const { id: connectionId, documentId, userId } = connectionData;

      // Clean up document event listener
      if (connectionData.updateHandler) {
        const doc = this.documentManager.getDocument(documentId);
        doc.off('update', connectionData.updateHandler);
      }

      // Remove connection
      this.connections.delete(ws);
      this.connectionManager.removeConnection(connectionId);

      // Update document connection count
      if (documentId) {
        const remainingConnections = this.connectionManager.getConnectionsByDocument(documentId);
        this.documentManager.updateConnectionCount(documentId, remainingConnections.length);
      }

      this.logger.info('WebSocket disconnected', {
        connectionId,
        documentId,
        userId,
        remainingConnections: documentId ?
          this.connectionManager.getConnectionsByDocument(documentId).length : 0
      });

    } catch (error) {
      this.logger.error('Failed to handle disconnection', error, {
        connectionId: connectionData.id
      });
    }
  }

  /**
   * Handle WebSocket errors
   * @param {WebSocket} ws - WebSocket connection
   * @param {Error} error - Error object
   * @param {Object} connectionData - Connection metadata
   */
  handleError(ws, error, connectionData) {
    this.logger.error('WebSocket error occurred', error, {
      connectionId: connectionData.id,
      documentId: connectionData.documentId,
      userId: connectionData.userId
    });
  }

  /**
   * Get connection statistics
   * @returns {Object} Connection statistics
   */
  getConnectionStats() {
    return {
      totalConnections: this.connections.size,
      connectionsByDocument: this.getConnectionsByDocument()
    };
  }

  /**
   * Get connections grouped by document
   * @returns {Object} Connections grouped by document ID
   */
  getConnectionsByDocument() {
    const result = {};
    for (const [ws, connectionData] of this.connections) {
      const { documentId } = connectionData;
      if (!result[documentId]) {
        result[documentId] = 0;
      }
      result[documentId]++;
    }
    return result;
  }
}

module.exports = YWebSocketHandler;

module.exports = WebSocketHandler;
