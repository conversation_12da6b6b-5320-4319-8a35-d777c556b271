version: '3.8'

services:
  realtime-yjs-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: realtime-yjs-server
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      - LOG_LEVEL=info
      - CORS_ORIGIN=*
      - YJS_PERSISTENCE=true
      - YJS_PERSISTENCE_DIR=/app/data
      - YJS_GC_ENABLED=true
      - YJS_CLEANUP_INTERVAL=300000
      - WS_PATH=/yjs
      - WS_MAX_CONNECTIONS=1000
      - WS_HEARTBEAT_INTERVAL=30000
    volumes:
      - ./logs:/app/logs
      - yjs-data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { if (res.statusCode === 200) process.exit(0); else process.exit(1); }).on('error', () => process.exit(1));"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yjs-network

  # Optional: Add a reverse proxy (nginx) for production
  nginx:
    image: nginx:alpine
    container_name: yjs-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - realtime-yjs-server
    restart: unless-stopped
    networks:
      - yjs-network
    profiles:
      - production

networks:
  yjs-network:
    driver: bridge

volumes:
  logs:
    driver: local
  yjs-data:
    driver: local
